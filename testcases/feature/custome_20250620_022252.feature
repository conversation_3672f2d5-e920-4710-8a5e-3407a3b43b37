Feature: custome
  As a mobile app user
  I want to navigate to custome and access its features
  So that I can use the application functionality effectively

  Background:
    Given I have launched the mobile application
    And I am on the main page of the application
Scenario: Validate "Ruang Murid" text presence
Given I launch the mobile application and am on the main screen
When I navigate to the Ruang Murid screen
Then I should see the "Ruang Murid" element
And the "Ruang Murid" should be visible
# Tags: custome, Mobile UI testing
