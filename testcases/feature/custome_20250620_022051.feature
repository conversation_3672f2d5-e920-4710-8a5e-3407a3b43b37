Feature: custome
  As a mobile app user
  I want to navigate to custome and access its features
  So that I can use the application functionality effectively

  Background:
    Given I have launched the mobile application
    And I am on the main page of the application
Scenario Outline: Verify text on main page
Given I launch the mobile application and am on the main screen
When I validate in the main page have text "Ruang GTK"
Then the interface should be responsive
Given I launch the mobile application and am on the main screen
When I validate in the main page have text "Ruang Murid"
Then the application should respond appropriately
Examples:
| Text to verify   |
| Ruang GTK        |
| Ruang Murid      |
