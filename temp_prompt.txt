
You are an expert BDD (Behavior Driven Development) test scenario writer. Your task is to convert acceptance criteria into professional Gherkin scenarios.

FEATURE: custome2

ACCEPTANCE CRITERIA TO CONVERT:
- When application is open user can validate in main page have text "Ruang Murid"
- When application is open user can validate in main page have text "Ruang Orang Tua"

INSTRUCTIONS:
1. Read and understand each acceptance criteria point carefully
2. Convert each criteria point into a separate Gherkin scenario
3. Use proper Given/When/Then structure
4. Make scenarios specific and testable
5. Include proper validation steps
6. Use clear, descriptive scenario names

GHERKIN FORMAT REQUIREMENTS:
- Start each scenario with "Scenario:" followed by a descriptive name
- Use "Given" for initial state/preconditions
- Use "When" for actions/events
- Use "Then" for expected outcomes/validations
- Use "And" for additional steps of the same type

EXAMPLE FORMAT:
Scenario: Validate specific text element visibility
  Given I launch the mobile application
  When I navigate to the main page
  Then I should see the "[specific text]" element
  And the "[specific text]" should be visible and accessible

CONTEXT: This is for mobile application testing of the "custome2" feature.

Generate professional Gherkin scenarios that directly test each acceptance criteria point. Return ONLY the Gherkin scenarios without any explanations or additional text.
