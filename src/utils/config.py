"""
Configuration Manager - Centralized configuration handling
"""

import os
from typing import Any, Optional
from dotenv import load_dotenv

class Config:
    """Centralized configuration manager"""

    def __init__(self):
        # Load environment variables
        load_dotenv()

        # Default configuration values
        self.PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

        # Default configuration values
        self.defaults = {
            "PROJECT_ROOT": self.PROJECT_ROOT,
            # AI Configuration
            # Options:
            # - "microsoft/Phi-3-mini-4k-instruct" (3.8GB, faster)
            # - "microsoft/Phi-3-medium-4k-instruct" (25GB, better quality)
            "PHI3_MODEL_PATH": "microsoft/Phi-3-mini-4k-instruct",
            "OPENAI_API_KEY": "",
            "HUGGINGFACE_TOKEN": "",

            # Chat Interface
            "CHAT_HOST": "localhost",
            "CHAT_PORT": 5000,
            "CHAT_DEBUG": True,

            # Mobile Testing
            "ANDROID_HOME": os.path.expanduser("~/Library/Android/sdk"),
            "ADB_PATH": os.path.expanduser("~/Library/Android/sdk/platform-tools/adb"),
            "EMULATOR_PATH": os.path.expanduser("~/Library/Android/sdk/emulator/emulator"),
            "DEFAULT_EMULATOR": "Pixel_7_API_34",
            "APPIUM_SERVER": "http://localhost:4723",

            # iOS Configuration
            "IOS_DEVICE_ID": "auto",
            "XCODE_PATH": "/Applications/Xcode.app",

            # Search Engines Configuration
            "SEARXNG_URL": "https://searx.be",
            "STARTPAGE_URL": "https://www.startpage.com",
            "BRAVE_SEARCH_URL": "https://search.brave.com",
            "DUCKDUCKGO_URL": "https://duckduckgo.com",
            "GOOGLE_URL": "https://www.google.com",

            # Search Engine Settings
            "SEARCH_MAX_RESULTS_PER_ENGINE": 10,
            "SEARCH_TOTAL_RESULTS_LIMIT": 20,
            "SEARCH_MIN_SUCCESSFUL_ENGINES": 1,
            "SEARCH_TIMEOUT": 15,  # Timeout per search engine request

            # Security & Evasion (Professional Grade) - MAXIMUM STEALTH
            "USE_TOR": True,
            "ROTATE_USER_AGENT": True,
            "BYPASS_CAPTCHA": True,
            "VIRUS_SCAN_ENABLED": True,
            "ROTATE_IP": True,
            "ROTATE_MAC": False,  # Disabled for demo - requires sudo
            "USE_PROXY_CHAINS": True,
            "STEALTH_MODE": False,
            "ANTI_DETECTION": True,
            "BROWSER_AUTOMATION": True,
            "HEADLESS_BROWSER": True,
            "RANDOM_DELAYS": True,
            "FINGERPRINT_RANDOMIZATION": True,

            # Ultra-Stealth Features (NEW)
            "HUMAN_BEHAVIOR_SIMULATION": True,
            "ADVANCED_HEADER_ROTATION": True,
            "REALISTIC_TIMING_PATTERNS": True,
            "BROWSER_FINGERPRINT_SPOOFING": True,
            "CANVAS_FINGERPRINT_RANDOMIZATION": True,
            "WEBGL_FINGERPRINT_SPOOFING": True,
            "AUDIO_FINGERPRINT_RANDOMIZATION": True,
            "SCREEN_RESOLUTION_SPOOFING": True,
            "TIMEZONE_RANDOMIZATION": True,
            "LANGUAGE_PREFERENCE_ROTATION": True,
            "COOKIE_BEHAVIOR_SIMULATION": True,
            "JAVASCRIPT_EXECUTION_DELAYS": True,
            "MOUSE_MOVEMENT_SIMULATION": True,
            "KEYBOARD_TIMING_SIMULATION": True,
            "SCROLL_BEHAVIOR_MIMICRY": True,
            "TAB_SWITCHING_PATTERNS": True,
            "NETWORK_TIMING_RANDOMIZATION": True,
            "DNS_OVER_HTTPS": True,
            "IP_GEOLOCATION_SPOOFING": True,

            # Enhanced Security Settings
            "ENHANCED_SECURITY": True,
            "VIRUSTOTAL_API_KEY": "",  # Add your VirusTotal API key
            "MAX_SCAN_FILE_SIZE": 50 * 1024 * 1024,  # 50MB
            "SCAN_TIMEOUT": 30,
            "THREAT_THRESHOLD": 3,
            "SUSPICIOUS_THRESHOLD": 1,

            # Professional Intelligence Configuration
            "PROFESSIONAL_MODE": False,
            "CAPTCHA_API_KEY": "",  # Add your 2captcha API key for professional CAPTCHA solving
            "PROFESSIONAL_REPORTING": True,
            "ADAPTIVE_LEARNING": True,
            "REAL_TIME_MONITORING": True,

            # Multi-Topic Learning Settings
            "MAX_CONCURRENT_TOPICS": 3,
            "TOPIC_MAX_RETRIES": 3,
            "TOPIC_RETRY_DELAY": 60,
            "MULTI_TOPIC_STATE_FILE": "data/multi_topic_state.json",

            # Content Analysis Settings
            "ANALYSIS_MODEL": "phi3:medium",
            "MIN_CONTENT_LENGTH": 100,
            "MAX_CONTENT_LENGTH": 50000,
            "CONTENT_CHUNK_SIZE": 1000,  # Made configurable instead of hardcoded
            "CHUNK_OVERLAP": 200,
            "MIN_CHUNK_LENGTH": 100,  # Minimum chunk length to process
            "MAX_CONTENT_FETCH_SIZE": 10000,  # Maximum content size to fetch from web pages
            "DNS_OVER_HTTPS": True,
            "SSL_BYPASS": True,

            # Proxy Configuration
            "PROXY_ROTATION_ENABLED": True,
            "PROXY_POOL_SIZE": 10,
            "PROXY_TIMEOUT": 10,
            "PROXY_RETRY_ATTEMPTS": 3,
            "FREE_PROXY_SOURCES": [
                "https://www.proxy-list.download/api/v1/get?type=http",
                "https://api.proxyscrape.com/v2/?request=get&protocol=http",
                "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt"
            ],

            # TOR Configuration
            "TOR_PROXY_HOST": "127.0.0.1",
            "TOR_PROXY_PORT": 9050,
            "TOR_CONTROL_PORT": 9051,
            "TOR_NEW_CIRCUIT_INTERVAL": 30,

            # Advanced Evasion
            "CLOUDFLARE_BYPASS": True,
            "RECAPTCHA_BYPASS": True,
            "HCAPTCHA_BYPASS": True,
            "RATE_LIMIT_BYPASS": True,
            "GEO_BLOCKING_BYPASS": True,
            "WAF_BYPASS": True,

            # File Paths
            "APK_STORAGE_PATH": "./data/apk",
            "ANALYSIS_OUTPUT_PATH": "./data/analysis",
            "GHERKIN_OUTPUT_PATH": "./testcases/feature",
            "SCREENSHOTS_PATH": "./data/screenshots",
            "VIDEOS_PATH": "./data/videos",

            # Logging
            "LOG_LEVEL": "WARNING",
            "LOG_FILE": "./logs/smtm.log",

            # Database
            "VECTOR_DB_PATH": "./data/vector_db",
            "KNOWLEDGE_DB_PATH": "./data/knowledge.db",

            # Testing
            "TEST_TIMEOUT": 30,
            "SCREENSHOT_ON_FAIL": True,
            "VIDEO_RECORDING": True,
            "ELEMENT_HIGHLIGHT": True,

            # Performance (ENHANCED FOR DEEP EXPLORATION)
            "MAX_CONCURRENT_TESTS": 3,
            "ANALYSIS_DEPTH": 15,  # ENHANCED: Increased from 8 to 15 for maximum depth exploration to reach 3000+ elements
            "CRAWL_DELAY": 0.8,  # ENHANCED: Reduced from 1.0 to 0.8 for faster exploration

            # Recovery and External App Handling (OPTIMIZED)
            "MAX_FEATURE_RECOVERY_ATTEMPTS": 2,  # Reduced from 3 to 2
            "MAX_EXTERNAL_APP_ELEMENTS": 20,
            "EXTERNAL_APP_TIMEOUT": 2,  # Reduced from 3 to 2 seconds
            "ACCEPT_PARTIAL_COMPLETION": True,

            # AI Analysis Performance Optimization (FIXED FOR BETTER PERFORMANCE)
            "GLOBAL_ANALYSIS_TIMEOUT_MINUTES": 60,  # Extended to 60 minutes to match MAX_ANALYSIS_DURATION_MINUTES
            "NAVIGATION_WAIT_TIMEOUT": 6,  # FIXED: Reduced from 8 to 6 seconds for faster navigation
            "APP_READY_TIMEOUT": 20,  # FIXED: Reduced from 30 to 20 seconds for faster startup
            "HIERARCHY_ANALYSIS_TIMEOUT": 90,  # FIXED: Reduced from 180 to 90 seconds per hierarchy level
            "TERMINAL_DEVICE_SYNC_TIMEOUT": 15,  # FIXED: Reduced from 20 to 15 seconds for faster sync
            "APP_RESPONSIVENESS_TIMEOUT": 3,  # FIXED: Reduced from 4 to 3 seconds for responsiveness
            "PATH_NAVIGATION_TIMEOUT": 30,  # FIXED: Reduced from 45 to 30 seconds for navigation
            "STABILITY_CHECK_COUNT": 5,  # ENHANCED: 5 checks for more thorough stability verification
            "MAX_NAVIGATION_TIMEOUTS": 10,  # ENHANCED: 10 timeouts for complex navigation patterns
            "CONSECUTIVE_FAILURES_THRESHOLD": 15,  # ENHANCED: 15 failures for resilient exploration
            "FORCED_COMPLETION_THRESHOLD": 100,  # ENHANCED: 100 minutes before forced completion (allows 20 more minutes)

            # Storage Management
            "MIN_STORAGE_MB": 200,  # Minimum storage required for APK installation
            "AGGRESSIVE_CLEANUP": True,  # Enable aggressive storage cleanup

            # AI Engine Configuration (OPTIMIZED FOR NATURAL CONVERSATIONAL RESPONSES)
            "OLLAMA_URL": "http://localhost:11434",
            "OLLAMA_MODEL": "phi3:medium",
            "OLLAMA_EMBEDDING_MODEL": "tazarov/all-minilm-l6-v2-f32:latest",
            "OLLAMA_TIMEOUT": 30,  # Standard timeout for regular responses
            "OLLAMA_FAST_TIMEOUT": 8,  # Fast timeout for knowledge synthesis (8 seconds)
            "OLLAMA_GHERKIN_TIMEOUT": 8,  # Fast timeout for Gherkin generation (8 seconds)
            "OLLAMA_KNOWLEDGE_TIMEOUT": 10,  # Knowledge engine specific timeout
            "OLLAMA_COMPREHENSIVE_TIMEOUT": 60,  # Optimized timeout for comprehensive responses (1 minute)
            "AI_TEMPERATURE": 1.0,  # Maximum natural conversation (same as Augment Code AI)
            "AI_NUM_PREDICT": 512,  # Default balanced length (increased from 256)

            # Vector Database Configuration
            "VECTOR_DB_COLLECTION_NAME": "knowledge",
            "VECTOR_DB_EMBEDDING_FUNCTION": "ollama",  # Options: ollama, openai, huggingface

            # Smart Dynamic Token Allocation (Claude-level capacity with speed optimization)
            "AI_NUM_PREDICT_QUICK": 256,      # Quick responses (simple questions)
            "AI_NUM_PREDICT_STANDARD": 512,   # Standard responses (default)
            "AI_NUM_PREDICT_DETAILED": 1024,  # Detailed explanations
            "AI_NUM_PREDICT_COMPREHENSIVE": 2048,  # Comprehensive analysis
            "AI_NUM_PREDICT_MAXIMUM": 4096,   # Maximum capacity (Claude-level)

            # Performance Optimization Settings
            "RESPONSE_CACHE_ENABLED": True,  # Re-enabled after implementing smart token allocation
            "RESPONSE_CACHE_TTL": 300,  # 5 minutes cache
            "KNOWLEDGE_CACHE_TTL": 600,  # 10 minutes cache for knowledge responses
            "CONCURRENT_PROCESSING": True,
            "FAST_RESPONSE_MODE": True,
            "PROMPT_OPTIMIZATION": "natural",  # Options: True (aggressive), "natural" (minimal), False (disabled)
            "NATURAL_CONVERSATION_MODE": True,  # Prioritize natural conversation over speed optimization
            "KNOWLEDGE_FALLBACK_ENABLED": True,  # Enable non-AI fallback for knowledge questions
            "SLOW_RESPONSE_THRESHOLD": 5.0,  # Threshold for slow response detection (seconds)
            "AUTO_SUMMARIZE_THRESHOLD": 0.7,  # 70% of context window before auto-summarization
            "CHARS_PER_TOKEN": 4,  # Approximate characters per token for estimation

            # Mobile Analysis Thresholds
            "MIN_ELEMENTS_DEFAULT": 5,
            "MIN_ELEMENTS_MAIN_APP": 8,
            "MIN_ELEMENTS_BROWSER": 10,
            "MIN_ELEMENTS_LAUNCHER": 15,
            "MIN_ELEMENTS_VOICE_TTS": 2,

            # Mobile Analysis Limits and Thresholds
            "MAX_BACK_ATTEMPTS": 10,
            "MAX_RETURN_TO_MAIN_ATTEMPTS": 8,
            "MAX_APP_RESTART_ATTEMPTS": 10,
            "FEATURE_TEXT_MIN_LENGTH": 2,
            "FEATURE_TEXT_MAX_LENGTH": 30,
            "MIN_INTERACTION_SIZE": 40,
            "MAX_INTERACTION_WIDTH": 800,
            "MAX_INTERACTION_HEIGHT": 200,
            "MAIN_CONTENT_AREA_TOP_MIN": 100,
            "MAIN_CONTENT_AREA_TOP_MAX": 1800,
            "MAIN_CONTENT_AREA_HEIGHT_MIN": 50,
            "PARALLEL_PROCESSING_MAX_WORKERS": 4,
            "ELEMENT_CHUNK_SIZE_DIVISOR": 4,  # len(elements) // this value
            "INTERACTION_SCORE_THRESHOLD": 3,
            "APP_READINESS_CHECK_ATTEMPTS": 10,
            "APP_READINESS_CHECK_INTERVAL": 2,
            "MIN_ELEMENTS_SYSTEM": 3,

            # Element Detection Limits (Configurable for 3000+ element support)
            "TARGET_ELEMENTS_COUNT": 3000,  # Target number of elements to find
            "MAX_ANALYSIS_DURATION_MINUTES": 60,  # Maximum analysis duration in minutes (extended from 40 to 60)
            "UNKNOWN_ELEMENT_THRESHOLD_PERCENT": 3.0,  # Maximum acceptable unknown elements percentage

            # Analysis Completion Thresholds (CONFIGURABLE: Lower thresholds for more thorough analysis)
            "MIN_FEATURE_COMPLETION_THRESHOLD": 30,  # Minimum % of features that must be analyzed (reduced from 80% to 30%)
            "MIN_SEGMENT_COMPLETION_THRESHOLD": 30,  # Minimum % of segments that must be analyzed (reduced from 80% to 30%)

            # Comprehensive Element Collection (CONFIGURABLE: AI behavior for complete structure discovery)
            "ENABLE_COMPREHENSIVE_ELEMENT_COLLECTION": True,  # Enable AI to collect ALL element locators systematically
            "COMPREHENSIVE_COLLECTION_DEPTH": 15,  # Maximum depth for comprehensive collection
            "COLLECT_ALL_HIERARCHICAL_STRUCTURE": True,  # Collect complete hierarchical structure
            "COLLECT_ALL_NAVIGATION_PATTERNS": True,  # Learn all navigation patterns
            "COLLECT_ALL_EXTERNAL_APP_ELEMENTS": True,  # Collect first page elements from external apps
            "MAX_ELEMENTS_SIMPLE_ANALYZER": 5000,  # Simple analyzer element limit (was hardcoded 20)
            "MAX_ELEMENTS_ADVANCED_DETECTOR": 10000,  # Advanced detector element limit (was hardcoded 50)

            # Autonomous Structure Discovery (CONFIGURABLE: AI learns UI structure without hardcoded values)
            "ENABLE_AUTONOMOUS_STRUCTURE_DISCOVERY": True,  # Enable AI to autonomously discover UI structure
            "AUTONOMOUS_MENU_DISCOVERY": True,  # AI discovers all menu structures autonomously
            "AUTONOMOUS_NAVIGATION_DISCOVERY": True,  # AI discovers all navigation patterns autonomously
            "AUTONOMOUS_HIERARCHICAL_ANALYSIS": True,  # AI analyzes complete hierarchical structure
            "MAX_MENU_DEPTH": 10,  # Maximum menu depth for autonomous discovery
            "COLLECT_EXTERNAL_APP_FIRST_PAGE_ONLY": True,  # Only collect first page when clicking leads to external apps
            "AUTONOMOUS_ELEMENT_CLASSIFICATION": True,  # AI classifies elements autonomously (navigation, content, etc.)
            "COMPREHENSIVE_MODE_BY_DEFAULT": True,  # Default to comprehensive mode for complete structure discovery
            "MAX_ELEMENTS_HIERARCHY_PARSING": 3000,  # Hierarchy parsing limit (was hardcoded 30)
            "MAX_ELEMENTS_EMERGENCY_DETECTION": 1000,  # Emergency detection limit (was hardcoded 10)
            "MAX_ELEMENTS_ATTRIBUTE_SCANNING": 5000,  # Attribute scanning limit (was hardcoded 50)
            "MAX_ELEMENTS_ENHANCED_XPATH": 8000,  # Enhanced XPath detection limit (was hardcoded 20)
            "MAX_SELECTORS_ENHANCED_XPATH": 20,  # Maximum selectors to try in enhanced XPath (was hardcoded 10)
            "MIN_ELEMENTS_THRESHOLD_PRIMARY": 5,  # Minimum elements for primary detection success
            "MAX_ELEMENTS_VALIDATION_BATCH": 1000,  # Maximum elements to validate in one batch
            "ELEMENT_DETECTION_EARLY_BREAK_THRESHOLD": 3000,  # When to break early (0 = never break early)
            "ENABLE_UNLIMITED_ELEMENT_DETECTION": True,  # Enable unlimited element detection for deep analysis

            # Real-time Problem Solving Configuration
            "ENABLE_REALTIME_PROBLEM_SOLVING": True,  # Enable AI to search internet and auto-fix issues
            "PROBLEM_SOLVING_BACKGROUND_MODE": True,  # Run problem solving in background without disturbing analysis
            "AUTO_IMPLEMENT_SOLUTIONS": True,  # Automatically implement found solutions
            "PROBLEM_DETECTION_INTERVAL_SECONDS": 30,  # How often to check for problems during analysis
            "MAX_PROBLEM_SOLVING_ATTEMPTS": 3,  # Maximum attempts to solve each problem
            "PROBLEM_SOLVING_TIMEOUT_SECONDS": 120,  # Timeout for each problem-solving attempt

            # Enhanced Element Classification Configuration
            "ENABLE_ENHANCED_ELEMENT_CLASSIFICATION": True,  # Enable advanced element classification
            "ELEMENT_CLASSIFICATION_CONFIDENCE_THRESHOLD": 0.7,  # Minimum confidence for classification
            "UNKNOWN_ELEMENT_FALLBACK_STRATEGIES": 5,  # Number of fallback strategies for unknown elements
            "WIDGET_PATTERN_MATCHING_ENABLED": True,  # Enable pattern matching for widget identification
            "ELEMENT_CONTEXT_ANALYSIS_ENABLED": True,  # Enable context-based element analysis

            # Sleep and Wait Durations (in seconds)
            "SLEEP_SHORT": 1,
            "SLEEP_MEDIUM": 2,
            "SLEEP_LONG": 3,
            "SLEEP_EXTENDED": 5,
            "SYSTEM_STABILIZE_WAIT": 5,

            # UI Stability and Verification
            "STABILITY_CHECK_COUNT": 3,
            "STABILITY_CHECK_INTERVAL": 1.0,
            "UI_HIERARCHY_CHECKS": 3,
            "APP_RESPONSIVENESS_CHECKS": 5,
            "APP_RESPONSIVENESS_TIMEOUT": 2,

            # Recovery and Retry Configuration
            "MAX_RECOVERY_ATTEMPTS": 5,
            "RECOVERY_WAIT_INTERVAL": 2,
            "MAX_NAVIGATION_RETRIES": 3,
            "FORCE_STOP_TIMEOUT": 10,

            # App Detection Keywords (Configurable for different languages and contexts)
            "BROWSER_KEYWORDS": ["chrome", "browser", "firefox", "safari", "edge"],
            "LAUNCHER_KEYWORDS": ["launcher", "home", "homescreen", "desktop"],
            "VOICE_TTS_KEYWORDS": ["tts", "voice", "speech", "audio", "sound"],
            "SYSTEM_KEYWORDS": ["system", "android", "settings"],
            "PROBLEMATIC_KEYWORDS": ["tts", "voice", "permission", "crash", "error", "failed", "timeout"],
            "LOADING_INDICATORS": ["loading", "please wait", "processing", "connecting", "buffering", "initializing"],

            # System App Detection Patterns (Configurable for different Android versions)
            "SYSTEM_APP_PATTERNS": ["launcher", "systemui", "settings", "android", ".home", ".system", "com.android"],

            # App Type Detection Keywords (Expandable for different app categories)
            "GAME_KEYWORDS": ["game", "heavy", "large", "gaming", "play"],
            "EDUCATION_KEYWORDS": ["education", "pendidikan", "learning", "school", "study", "tutorial"],

            # File Naming Patterns
            "ANALYSIS_STATE_FILE": "logs/analysis_state.json",
            "LIVE_SAVE_PATTERN": "{app_name}_{timestamp}_live_analysis.xlsx",
            "TERMINAL_LOG_PATTERN": "ai_analysis_{timestamp}_terminal.txt",

            # Performance Optimization
            "VERSION_CODE_THRESHOLD": 1000,  # For complex app detection
            "ELEMENT_CHECK_LIMIT": 10,  # Number of elements to check for loading indicators
            "MAX_HISTORY_SIZE": 50,  # Maximum state history size

            # Smart Goal-Oriented Navigation Configuration
            "MAX_BACK_ATTEMPTS": 5,  # Maximum back press attempts for navigation
            "NAVIGATION_SLEEP": 1,  # Sleep duration between navigation actions (seconds)
            "GOAL_VERIFICATION_TIMEOUT": 3,  # Timeout for goal verification checks (seconds)
            "SMART_NAVIGATION_ENABLED": True,  # Enable smart goal-oriented navigation

            # Enhanced App Readiness Configuration
            "MIN_INTERACTIVE_ELEMENTS": 3,  # Minimum clickable elements for app readiness
            "MIN_UI_ELEMENTS": 10,  # Minimum total UI elements for app readiness
            "MIN_CONTENT_INDICATORS": 2,  # Minimum content indicators for app readiness
            "APP_READINESS_TIMEOUT": 30,  # Maximum time to wait for app readiness (seconds)
            "READINESS_CHECK_INTERVAL": 2,  # Interval between readiness checks (seconds)

            # Dynamic Content Indicators (configurable for any app type)
            "CONTENT_INDICATORS": {
                "educational": [
                    "ruang", "pendidikan", "guru", "murid", "belajar", "pembelajaran",
                    "pelatihan", "mandiri", "kursus", "materi", "siswa", "pengajar"
                ],
                "navigation": [
                    "menu", "beranda", "home", "dashboard", "utama", "main",
                    "navigation", "nav", "tab", "halaman", "page"
                ],
                "interactive": [
                    "button", "tombol", "klik", "click", "pilih", "select",
                    "masuk", "login", "daftar", "register", "mulai", "start"
                ],
                "general": [
                    "aplikasi", "app", "layanan", "service", "fitur", "feature",
                    "konten", "content", "informasi", "information"
                ]
            },

            # App-Specific Readiness Patterns (configurable per app)
            # Use environment variable APP_PACKAGE to set current app package
            # Use environment variable APP_PATTERNS to set app-specific patterns (comma-separated)
            "APP_SPECIFIC_PATTERNS": {
                "default": [
                    "main", "home", "menu", "dashboard", "beranda", "utama"
                ]
            },

            # Current app configuration (can be overridden by environment variables)
            "CURRENT_APP_PACKAGE": os.getenv("APP_PACKAGE", "com.kemendikdasmen.rumahpendidikan"),
            "CURRENT_APP_NAME": os.getenv("APP_NAME", "Rumah Pendidikan"),
            "CURRENT_APP_PATTERNS": os.getenv("APP_PATTERNS", "rumah pendidikan,ruang gtk,ruang murid,pelatihan mandiri,beranda,menu utama,dashboard").split(",")
        }

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        # First check environment variables
        env_value = os.getenv(key)
        if env_value is not None:
            return self._convert_type(env_value)

        # Then check defaults
        if key in self.defaults:
            return self.defaults[key]

        # Return provided default
        return default

    def set(self, key: str, value: Any):
        """Set configuration value"""
        self.defaults[key] = value

    def _convert_type(self, value: str) -> Any:
        """Convert string environment variable to appropriate type"""
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'

        # Integer conversion
        try:
            return int(value)
        except ValueError:
            pass

        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass

        # Return as string
        return value

    def get_android_sdk_path(self) -> str:
        """Get Android SDK path"""
        return self.get("ANDROID_HOME")

    def get_adb_path(self) -> str:
        """Get ADB executable path"""
        return self.get("ADB_PATH")

    def get_emulator_path(self) -> str:
        """Get emulator executable path"""
        return self.get("EMULATOR_PATH")

    def get_data_paths(self) -> dict:
        """Get all data storage paths"""
        return {
            "apk": self.get("APK_STORAGE_PATH"),
            "analysis": self.get("ANALYSIS_OUTPUT_PATH"),
            "gherkin": self.get("GHERKIN_OUTPUT_PATH"),
            "screenshots": self.get("SCREENSHOTS_PATH"),
            "videos": self.get("VIDEOS_PATH"),
            "vector_db": self.get("VECTOR_DB_PATH"),
            "knowledge_db": self.get("KNOWLEDGE_DB_PATH")
        }

    def ensure_directories(self):
        """Ensure all required directories exist"""
        paths = self.get_data_paths()
        for path in paths.values():
            os.makedirs(path, exist_ok=True)

        # Also ensure logs directory
        os.makedirs("logs", exist_ok=True)
