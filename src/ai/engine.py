"""
AI Engine - Core AI processing and command execution
Enhanced with full project access and self-fixing capabilities
Integrates with phi-3-medium for autonomous execution
"""

import requests
from typing import Dict, Any, List, Optional
from loguru import logger
import os
import sys
import traceback
import subprocess
import ast
import json
import time
from pathlib import Path

from utils.config import Config
from mobile.analyzer import MobileAnalyzer
from mobile.tester import MobileTester
from mobile.robust_element_collector import RobustElementCollector
from ai.gherkin_generator import GherkinGenerator
from ai.self_learning import SelfLearningAgent
from ai.performance_optimizer import PerformanceOptimizer
from utils.console import get_centralized_console

console = get_centralized_console()


class AIEngine:
    """Enhanced AI Engine with full project access and self-fixing capabilities"""

    def __init__(self):
        # Initialize config first
        self.config = Config()
        self.ollama_url = self.config.get("OLLAMA_URL")
        self.model_name = self.config.get("OLLAMA_MODEL")
        
        # Initialize components
        self.mobile_analyzer = MobileAnalyzer()
        self.mobile_analyzer.config = self.config
        self.mobile_analyzer.element_collector = RobustElementCollector(dict(self.config.defaults))
        self.mobile_tester = MobileTester()
        self.gherkin_generator = GherkinGenerator()
        self.self_learning_agent = None
        self.is_initialized = False

        # Performance optimization for 1-5 second responses
        self.performance_optimizer = PerformanceOptimizer(self.config)

        # Enhanced capabilities
        self.project_analyzer = None
        self.self_fixer = None
        self.code_analyzer = None
        self.codebase_indexer = None
        self.error_tracker = []
        self.project_root = Path.cwd()
        self.enhanced_mode = True
        self.autonomous_mode = True

    async def initialize(self):
        """Initialize the AI engine and all components with enhanced capabilities"""
        try:
            console.print("[yellow]Initializing Enhanced AI Engine...[/yellow]")

            # Check Ollama connection
            await self._check_ollama_connection()

            # Initialize core components with config
            self.mobile_analyzer = MobileAnalyzer()
            self.mobile_analyzer.config = self.config
            self.mobile_analyzer.element_collector = RobustElementCollector(dict(self.config.defaults))
            self.mobile_tester = MobileTester()
            self.gherkin_generator = GherkinGenerator()
            self.self_learning_agent = SelfLearningAgent()

            # Initialize enhanced components
            await self._initialize_enhanced_components()

            # Initialize all components
            await self.mobile_analyzer.initialize()
            await self.mobile_tester.initialize()
            await self.self_learning_agent.initialize()

            # Initialize Gherkin generator with RAG integration
            await self.gherkin_generator.initialize(self.self_learning_agent)

            self.is_initialized = True
            console.print(
                "[green]Enhanced AI Engine initialized successfully![/green]"
            )
            console.print(
                "[cyan]🚀 Full project access and self-fixing capabilities enabled![/cyan]"
            )
            logger.info("Enhanced AI Engine initialization completed")

        except Exception as e:
            console.print(f"[red]Failed to initialize AI Engine: {e}[/red]")
            logger.error(
                f"AI Engine initialization failed: {e}", exc_info=True
            )
            raise

    async def _initialize_enhanced_components(self):
        """Initialize enhanced AI components for full project access and self-fixing"""
        try:
            console.print("[cyan]Initializing enhanced components...[/cyan]")

            # Initialize project analyzer
            from ai.project_analyzer import ProjectAnalyzer
            self.project_analyzer = ProjectAnalyzer(self.project_root)

            # Initialize self-fixing system
            from ai.self_fixer import SelfFixer
            self.self_fixer = SelfFixer(self.project_root, self)

            # Initialize code analyzer
            from ai.code_analyzer import CodeAnalyzer
            self.code_analyzer = CodeAnalyzer(self.project_root)

            # Initialize codebase indexer with full autonomous access
            from ai.codebase_indexer import CodebaseIndexer
            self.codebase_indexer = CodebaseIndexer(self.project_root)

            console.print("[green]✓ Enhanced components initialized[/green]")
            console.print("[yellow]🤖 AI granted full autonomous access to codebase[/yellow]")

        except ImportError as e:
            console.print(f"[yellow]⚠ Some enhanced components not available: {e}[/yellow]")
            logger.warning(f"Enhanced components import failed: {e}")
        except Exception as e:
            console.print(f"[red]Enhanced components initialization failed: {e}[/red]")
            logger.error(f"Enhanced components failed: {e}", exc_info=True)

    async def _check_ollama_connection(self):
        """Check connection to Ollama server"""
        try:
            console.print(
                f"[yellow]Connecting to Ollama at {self.ollama_url}...[/yellow]"
            )

            # Check if Ollama is running
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model["name"] for model in models]

                if self.model_name in model_names:
                    console.print(
                        "[green]✅ Connected to Ollama! "
                        f"Model '{self.model_name}' is available.[/green]"
                    )
                else:
                    console.print(
                        "[yellow]⚠️  Ollama connected but "
                        f"model '{self.model_name}' not found.[/yellow]"
                    )
                    console.print(
                        "[yellow]Available models: "
                        f"{', '.join(model_names)}[/yellow]"
                    )
                    console.print(
                        "[yellow]Please run: "
                        f"ollama pull {self.model_name}[/yellow]"
                    )
                    raise Exception(
                        f"Model {self.model_name} not available in Ollama"
                    )
            else:
                raise Exception(
                    f"Ollama server returned status {response.status_code}"
                )

        except requests.exceptions.ConnectionError:
            console.print(
                f"[red]❌ Cannot connect to Ollama at {self.ollama_url}[/red]"
            )
            console.print(
                "[yellow]Please make sure Ollama is running: "
                "ollama serve[/yellow]"
            )
            raise Exception("Ollama server not accessible")
        except Exception as e:
            console.print(f"[red]Failed to connect to Ollama: {e}[/red]")
            logger.error(f"Ollama connection failed: {e}", exc_info=True)
            raise

    async def learn_topic(self, topic: str) -> Dict[str, Any]:
        """Learn about a specific topic using the self-learning agent.
        
        Args:
            topic: The topic to learn about
            
        Returns:
            Dict[str, Any]: Results of the learning process
        """
        if not self.is_initialized or not self.self_learning_agent:
            return {"success": False, "error": "AI Engine or learning agent not initialized"}
            
        try:
            console.print(f"[cyan]Learning about topic: {topic}[/cyan]")
            result = await self.self_learning_agent.learn_topic(topic)
            return {"success": True, "result": result}
        except Exception as e:
            logger.error(f"Failed to learn topic {topic}: {e}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    async def learn_multiple_topics(self, topics: List[str]) -> Dict[str, Any]:
        """Learn about multiple topics using the self-learning agent.
        
        Args:
            topics: List of topics to learn about
            
        Returns:
            Dict[str, Any]: Results of the learning process for each topic
        """
        if not self.is_initialized or not self.self_learning_agent:
            return {"success": False, "error": "AI Engine or learning agent not initialized"}
            
        try:
            console.print(f"[cyan]Learning about multiple topics: {', '.join(topics)}[/cyan]")
            results = {}
            for topic in topics:
                result = await self.self_learning_agent.learn_topic(topic)
                results[topic] = result
            return {"success": True, "results": results}
        except Exception as e:
            logger.error(f"Failed to learn multiple topics: {e}", exc_info=True)
            return {"success": False, "error": str(e)}
    
    async def process_command(
        self, command: str, user_input: str = "", mobile_os_callback=None
    ) -> Dict[str, Any]:
        """Process a command autonomously with enhanced capabilities"""
        if not self.is_initialized:
            return {"success": False, "error": "AI Engine not initialized"}

        try:
            console.print(f"[cyan]Processing command: {command}[/cyan]")

            # Original commands with enhanced custom analysis support
            if command == "/ai-analysis" or command.startswith("/ai-analysis "):
                # Extract custom analysis instructions if provided
                custom_analysis_instructions = None

                if command.startswith("/ai-analysis "):
                    # Has parameters
                    custom_analysis_instructions = command[13:].strip()
                    if not custom_analysis_instructions:
                        # Empty parameters - default to comprehensive
                        custom_analysis_instructions = "comprehensive"
                        console.print("[yellow]🎯 Empty parameters provided - defaulting to comprehensive mode[/yellow]")
                        console.print("[green]🧠 AI will autonomously discover complete UI structure[/green]")
                elif command == "/ai-analysis":
                    # No parameters at all - default to comprehensive
                    custom_analysis_instructions = "comprehensive"
                    console.print("[yellow]🎯 No parameters provided - defaulting to comprehensive mode[/yellow]")
                    console.print("[green]🧠 AI will autonomously discover complete UI structure[/green]")

                return await self._handle_ai_analysis(mobile_os_callback, custom_analysis_instructions)
            elif command == "/generate-gherkin" or command.startswith("/generate-gherkin "):
                # Extract custom instructions if provided
                custom_instructions = ""
                excel_file = ""
                if command.startswith("/generate-gherkin "):
                    param = command[18:].strip()
                    # Check if the parameter is an Excel file
                    if param.endswith(".xlsx"):
                        excel_file = param
                    else:
                        custom_instructions = param
                return await self._handle_generate_gherkin(custom_instructions, excel_file)
            elif command == "/mobile-test" or command.startswith("/mobile-test "):
                # Extract specific file if provided
                specific_file = ""
                if command.startswith("/mobile-test "):
                    specific_file = command[13:].strip()
                return await self._handle_mobile_test(mobile_os_callback, specific_file)
            elif command.startswith("/self-learning"):
                topic = user_input or command.replace("/self-learning", "").strip()
                return await self._handle_self_learning(topic)
            elif command.startswith("/multi-learning"):
                topics = user_input or command.replace("/multi-learning", "").strip()
                return await self._handle_multi_topic_learning(topics)

            # Enhanced commands for project access and self-fixing
            elif command == "/analyze-project":
                return await self._handle_project_analysis()
            elif command.startswith("/analyze-file "):
                file_path = command[14:].strip()
                return await self._handle_file_analysis(file_path)
            elif command == "/health-check":
                return await self._handle_health_check()
            elif command.startswith("/fix-error "):
                error_description = command[11:].strip()
                return await self._handle_error_fixing(error_description)
            elif command == "/self-diagnose":
                return await self._handle_self_diagnosis()
            elif command.startswith("/modify-code "):
                modification_request = command[13:].strip()
                return await self._handle_code_modification(modification_request)
            elif command.startswith("/search-code "):
                search_query = command[13:].strip()
                return await self._handle_code_search(search_query)
            elif command == "/project-tree":
                return await self._handle_project_tree()
            elif command == "/fix-history":
                return await self._handle_fix_history()
            elif command.startswith("/quality-check "):
                file_path = command[15:].strip()
                return await self._handle_quality_check(file_path)
            elif command == "/help":
                return await self._handle_help()
            else:
                return {
                    "success": False,
                    "error": f"Unknown command: {command}. Type '/help' for available commands."
                }

        except Exception as e:
            logger.error(f"Command processing failed: {e}", exc_info=True)

            # Attempt self-fixing if enabled
            if self.enhanced_mode and self.self_fixer:
                console.print("[yellow]🔧 Attempting automatic error recovery...[/yellow]")
                fix_result = await self.self_fixer.handle_error(e, {"command": command})

                if fix_result.get("success"):
                    console.print("[green]✅ Error automatically resolved, retrying command...[/green]")
                    return await self.process_command(command, user_input, mobile_os_callback)
                else:
                    console.print("[red]❌ Automatic fix failed[/red]")

            return {"success": False, "error": str(e)}

    async def _handle_ai_analysis(
        self, mobile_os_callback=None, custom_analysis_instructions=None, web_search_tool=None, web_fetch_tool=None
    ) -> Dict[str, Any]:
        """Handle /ai-analysis command with hierarchical navigation and autonomous problem solving"""
        if custom_analysis_instructions:
            console.print(
                f"[yellow]Starting custom AI-powered mobile analysis with hierarchical navigation: {custom_analysis_instructions}[/yellow]"
            )
        else:
            console.print(
                "[yellow]Starting comprehensive AI-powered mobile analysis with hierarchical navigation and autonomous problem solving...[/yellow]"
            )

        console.print("[bold cyan]🎯 IMPLEMENTING USER'S HIERARCHICAL NAVIGATION REQUIREMENTS[/bold cyan]")
        console.print("[yellow]📋 Enhanced with complete hierarchical structure collection[/yellow]")

        try:
            # Configure web search tools for autonomous problem solving
            if web_search_tool and web_fetch_tool and self.mobile_analyzer:
                self.mobile_analyzer.set_web_search_tools(web_search_tool, web_fetch_tool)
                console.print("[green]✅ Autonomous internet research enabled[/green]")

            # Enhanced autonomous mobile analysis with hierarchical navigation
            result = await self.mobile_analyzer.analyze_mobile_app(
                mobile_os_callback, custom_analysis_instructions
            )

            if result["success"]:
                console.print(
                    "[green]Hierarchical mobile analysis completed successfully![/green]"
                )

                # Display hierarchical analysis results
                if "data" in result:
                    data = result["data"]
                    console.print(f"[cyan]📊 Analysis Results Summary:[/cyan]")
                    console.print(f"[green]   • Total elements: {data.get('elements_found', 0)}[/green]")
                    console.print(f"[green]   • Features analyzed: {data.get('features_analyzed', 0)}[/green]")
                    console.print(f"[green]   • Analysis duration: {data.get('total_analysis_duration', 'N/A')}[/green]")

                    # Check if hierarchical requirements are met
                    elements_found = data.get('elements_found', 0)
                    if elements_found >= 3000:
                        console.print(f"[bold green]🎉 HIERARCHICAL ANALYSIS TARGET ACHIEVED: {elements_found} elements![/bold green]")
                    else:
                        console.print(f"[yellow]📊 Elements found: {elements_found} (Target: 3000+)[/yellow]")

                return {
                    "success": True,
                    "message": "Hierarchical mobile analysis completed",
                    "data": result.get("data", {})
                }
            else:
                console.print(
                    f"[red]Hierarchical mobile analysis failed: {result['error']}[/red]"
                )
                return result

        except Exception as e:
            logger.error(f"Hierarchical AI analysis failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_generate_gherkin(self, custom_instructions: str = "", excel_file: str = "") -> Dict[str, Any]:
        """Handle /generate-gherkin command with optional custom instructions or Excel file"""
        if excel_file:
            console.print(
                f"[yellow]Generating Gherkin test scenarios from Excel file: {excel_file}[/yellow]"
            )

            # Resolve the Excel file path - check multiple locations
            resolved_excel_path = self._resolve_excel_file_path(excel_file)
            if not resolved_excel_path:
                error_msg = f"Excel file not found: {excel_file}. Checked locations: current directory, testcases/, data/analysis/"
                console.print(f"[red]{error_msg}[/red]")
                return {"success": False, "error": error_msg}

            console.print(f"[cyan]Reading Excel file: {resolved_excel_path}[/cyan]")
            excel_file = resolved_excel_path

        elif custom_instructions:
            console.print(
                f"[yellow]Generating Gherkin test scenarios with custom instructions: {custom_instructions}[/yellow]"
            )
        else:
            console.print(
                "[yellow]Generating Gherkin test scenarios from latest analysis...[/yellow]"
            )

        try:
            if excel_file:
                result = await self.gherkin_generator.generate_scenarios_from_excel(excel_file)
            else:
                result = await self.gherkin_generator.generate_scenarios(custom_instructions)

            if result["success"]:
                console.print(
                    "[green]Gherkin scenarios generated successfully![/green]"
                )
                return result
            else:
                console.print(
                    f"[red]Gherkin generation failed: {result['error']}[/red]"
                )
                return result

        except Exception as e:
            logger.error(f"Gherkin generation failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    def _resolve_excel_file_path(self, excel_file: str) -> Optional[str]:
        """Resolve Excel file path by checking multiple locations"""
        import os

        # List of possible locations to check
        possible_paths = [
            excel_file,  # Current directory
            os.path.join("testcases", excel_file),  # testcases directory
            os.path.join("data", "analysis", excel_file),  # analysis directory
            os.path.join(self.config.get("ANALYSIS_OUTPUT_PATH", "./data/analysis"), excel_file),  # configured analysis path
        ]

        for path in possible_paths:
            if os.path.exists(path):
                console.print(f"[green]Found Excel file at: {path}[/green]")
                return path

        return None

    async def _handle_mobile_test(
        self, mobile_os_callback=None, specific_file: str = ""
    ) -> Dict[str, Any]:
        """Handle /mobile-test command with comprehensive terminal logging"""
        from utils.terminal_logger import start_ai_analysis_logging, end_ai_analysis_logging, log_ai_analysis_event
        from datetime import datetime

        if specific_file:
            console.print(
                f"[yellow]Starting mobile testing for specific file: {specific_file}[/yellow]"
            )
        else:
            console.print(
                "[yellow]Starting autonomous mobile testing for all Gherkin files...[/yellow]"
            )

        # Start comprehensive terminal logging for mobile-test
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_id = start_ai_analysis_logging("mobile_test", "mobile_test")

        if session_id:
            console.print(f"[green]📝 Terminal logging started: mobile-test-{timestamp}_terminal.txt[/green]")
            log_ai_analysis_event("SESSION_START", f"Mobile test session started with ID: {session_id}")
        else:
            console.print("[yellow]⚠️ Terminal logging could not be started[/yellow]")

        try:
            # Log mobile test start
            if specific_file:
                log_ai_analysis_event("MOBILE_TEST_START", f"Beginning mobile test execution for file: {specific_file}")
            else:
                log_ai_analysis_event("MOBILE_TEST_START", "Beginning automated mobile test execution for all files")

            result = await self.mobile_tester.run_tests(mobile_os_callback, specific_file)

            if result["success"]:
                console.print(
                    "[green]Mobile tests completed successfully![/green]"
                )

                # Display detailed results if available
                if 'data' in result:
                    data = result['data']
                    console.print(f"[cyan]Tests executed: {data.get('tests_executed', 0)}[/cyan]")
                    console.print(f"[green]Tests passed: {data.get('tests_passed', 0)}[/green]")
                    console.print(f"[red]Tests failed: {data.get('tests_failed', 0)}[/red]")

                    if data.get('report_file'):
                        console.print(f"[cyan]Report saved to: {data['report_file']}[/cyan]")

                # Log successful completion
                log_ai_analysis_event("MOBILE_TEST_SUCCESS",
                    f"Tests completed - Executed: {result.get('data', {}).get('tests_executed', 0)}, "
                    f"Passed: {result.get('data', {}).get('tests_passed', 0)}, "
                    f"Failed: {result.get('data', {}).get('tests_failed', 0)}")

                # End terminal logging with success
                end_ai_analysis_logging(success=True)

                if session_id:
                    console.print(f"[green]📝 Terminal log saved: logs/mobile-test-{timestamp}_terminal.txt[/green]")

                return result
            else:
                console.print(
                    f"[red]Mobile testing failed: {result['error']}[/red]"
                )

                # Log failure
                log_ai_analysis_event("MOBILE_TEST_FAILURE", f"Mobile test failed: {result.get('error', 'Unknown error')}")

                # End terminal logging with failure
                end_ai_analysis_logging(success=False, error=result.get('error', 'Unknown error'))

                if session_id:
                    console.print(f"[yellow]📝 Terminal log saved: logs/mobile-test-{timestamp}_terminal.txt[/yellow]")

                return result

        except Exception as e:
            logger.error(f"Mobile testing failed: {e}", exc_info=True)

            # Log exception
            log_ai_analysis_event("MOBILE_TEST_EXCEPTION", f"Exception occurred: {str(e)}")

            # End terminal logging with failure
            end_ai_analysis_logging(success=False, error=str(e))

            if session_id:
                console.print(f"[red]📝 Terminal log saved: logs/mobile-test-{timestamp}_terminal.txt[/red]")

            return {"success": False, "error": str(e)}

    async def _handle_self_learning(self, topic: str) -> Dict[str, Any]:
        """Handle /self-learning command"""
        if not topic:
            return {
                "success": False,
                "error": "Please provide a topic to learn about"
            }

        console.print(
            f"[yellow]Starting self-learning on topic: {topic}[/yellow]"
        )

        try:
            if self.self_learning_agent is None:
                return {"success": False, "error": "Self learning agent is not initialized"}
            result = await self.self_learning_agent.learn_topic(topic)

            if result["success"]:
                console.print(
                    "[green]Self-learning completed successfully![/green]"
                )
                return result
            else:
                console.print(
                    f"[red]Self-learning failed: {result['error']}[/red]"
                )
                return result

        except Exception as e:
            logger.error(f"Self-learning failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_multi_topic_learning(self, topics: str) -> Dict[str, Any]:
        """Handle multi-topic learning command"""
        if not topics:
            return {
                "success": False,
                "error": "Please provide topics to learn about"
            }

        console.print(
            f"[yellow]Starting multi-topic learning: {topics}[/yellow]"
        )

        try:
            if not self.self_learning_agent:
                return {"success": False, "error": "Self learning agent not initialized"}
            result = await self.self_learning_agent.learn_multiple_topics(topics)

            if result["success"]:
                console.print(
                    "[green]Multi-topic learning completed successfully![/green]"
                )
                return result
            else:
                console.print(
                    f"[red]Multi-topic learning failed: {result['error']}[/red]"
                )
                return result

        except Exception as e:
            logger.error(f"Multi-topic learning failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    # Enhanced command handlers for project access and self-fixing

    async def _handle_project_analysis(self) -> Dict[str, Any]:
        """Handle /analyze-project command"""
        console.print("[yellow]🔍 Starting comprehensive project analysis...[/yellow]")

        try:
            if not self.project_analyzer:
                return {"success": False, "error": "Project analyzer not available"}

            result = await self.project_analyzer.analyze_project()

            if result.get("success"):
                console.print("[green]✅ Project analysis completed![/green]")

                # Display summary
                stats = result.get("statistics", {})
                console.print(f"[cyan]📊 Found {stats.get('total_files', 0)} code files, "
                            f"{stats.get('total_functions', 0)} functions, "
                            f"{stats.get('total_classes', 0)} classes[/cyan]")

                issues = result.get("issues", [])
                if issues:
                    console.print(f"[yellow]⚠️ Found {len(issues)} issues[/yellow]")

                return {"success": True, "data": result}
            else:
                return result

        except Exception as e:
            logger.error(f"Project analysis failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_file_analysis(self, file_path: str) -> Dict[str, Any]:
        """Handle /analyze-file command"""
        if not file_path:
            return {"success": False, "error": "Please specify a file path"}

        console.print(f"[yellow]🔍 Analyzing file: {file_path}[/yellow]")

        try:
            if not self.code_analyzer:
                return {"success": False, "error": "Code analyzer not available"}

            result = await self.code_analyzer.analyze_code_file(file_path)

            if result.get("success"):
                console.print(f"[green]✅ File analysis completed for {file_path}![/green]")

                # Display analysis summary
                analysis = result.get("analysis", {})
                console.print(f"[cyan]📊 Language: {analysis.get('language', 'unknown')}, "
                            f"Lines: {analysis.get('lines_of_code', 0)}, "
                            f"Functions: {len(analysis.get('functions', []))}, "
                            f"Classes: {len(analysis.get('classes', []))}, "
                            f"Issues: {len(analysis.get('issues', []))}[/cyan]")

                return result
            else:
                return result

        except Exception as e:
            logger.error(f"File analysis failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_health_check(self) -> Dict[str, Any]:
        """Handle /health-check command"""
        console.print("[yellow]🏥 Running comprehensive health check...[/yellow]")

        try:
            if not self.self_fixer:
                return {"success": False, "error": "Self-fixer not available"}

            health_report = await self.self_fixer.diagnose_project_health()

            overall_health = health_report.get("overall_health", "unknown")
            issues_count = len(health_report.get("issues_found", []))

            health_color = {
                "excellent": "green",
                "good": "cyan",
                "fair": "yellow",
                "poor": "red"
            }.get(overall_health, "white")

            console.print(f"[{health_color}]🏥 Project health: {overall_health.upper()}[/{health_color}]")
            console.print(f"[cyan]📊 Issues found: {issues_count}[/cyan]")

            if issues_count > 0:
                console.print("[yellow]⚠️ Issues detected - run /self-diagnose for details[/yellow]")

            return {"success": True, "data": health_report}

        except Exception as e:
            logger.error(f"Health check failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_error_fixing(self, error_description: str) -> Dict[str, Any]:
        """Handle /fix-error command"""
        if not error_description:
            return {"success": False, "error": "Please describe the error to fix"}

        console.print(f"[yellow]🔧 Attempting to fix error: {error_description}[/yellow]")

        try:
            if not self.self_fixer:
                return {"success": False, "error": "Self-fixer not available"}

            # Create a mock error for the fixer to handle
            mock_error = Exception(error_description)
            result = await self.self_fixer.handle_error(mock_error, {"manual_request": True})

            if result.get("success"):
                console.print("[green]✅ Error fixed successfully![/green]")
            else:
                console.print("[yellow]⚠️ Could not automatically fix error, providing guidance[/yellow]")

            return result

        except Exception as e:
            logger.error(f"Error fixing failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_self_diagnosis(self) -> Dict[str, Any]:
        """Handle /self-diagnose command"""
        console.print("[yellow]🔍 Running comprehensive self-diagnosis...[/yellow]")

        try:
            diagnosis_results = {}

            # Project analysis
            if self.project_analyzer:
                console.print("[cyan]📁 Analyzing project structure...[/cyan]")
                project_result = await self.project_analyzer.analyze_project()
                diagnosis_results["project_analysis"] = project_result

            # Health check
            if self.self_fixer:
                console.print("[cyan]🏥 Checking project health...[/cyan]")
                health_result = await self.self_fixer.diagnose_project_health()
                diagnosis_results["health_check"] = health_result

            # Component status
            diagnosis_results["component_status"] = {
                "ai_engine": "initialized" if self.is_initialized else "not_initialized",
                "mobile_analyzer": "available" if self.mobile_analyzer else "unavailable",
                "mobile_tester": "available" if self.mobile_tester else "unavailable",
                "gherkin_generator": "available" if self.gherkin_generator else "unavailable",
                "self_learning_agent": "available" if self.self_learning_agent else "unavailable",
                "project_analyzer": "available" if self.project_analyzer else "unavailable",
                "self_fixer": "available" if self.self_fixer else "unavailable",
                "code_analyzer": "available" if self.code_analyzer else "unavailable"
            }

            console.print("[green]✅ Self-diagnosis completed![/green]")
            return {"success": True, "data": diagnosis_results}

        except Exception as e:
            logger.error(f"Self-diagnosis failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_code_modification(self, modification_request: str) -> Dict[str, Any]:
        """Handle /modify-code command"""
        if not modification_request:
            return {"success": False, "error": "Please specify modification request"}

        console.print(f"[yellow]✏️ Processing code modification: {modification_request}[/yellow]")

        try:
            if not self.code_analyzer:
                return {"success": False, "error": "Code analyzer not available"}

            # This is a simplified implementation - could be enhanced with AI-powered parsing
            console.print("[yellow]⚠️ Code modification requires manual implementation[/yellow]")
            console.print(f"[cyan]Request: {modification_request}[/cyan]")

            return {
                "success": False,
                "error": "Automated code modification not yet implemented",
                "guidance": "Please manually implement the requested changes"
            }

        except Exception as e:
            logger.error(f"Code modification failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_code_search(self, search_query: str) -> Dict[str, Any]:
        """Handle /search-code command"""
        if not search_query:
            return {"success": False, "error": "Please specify search query"}

        console.print(f"[yellow]🔍 Searching code for: {search_query}[/yellow]")

        try:
            if not self.project_analyzer:
                return {"success": False, "error": "Project analyzer not available"}

            results = await self.project_analyzer.search_in_files(search_query, ["py", "js", "ts"])

            console.print(f"[green]✅ Found {len(results)} matches![/green]")

            # Display first few results
            for i, result in enumerate(results[:5]):
                console.print(f"[cyan]{i+1}. {result.get('file', '')}:{result.get('line', '')}[/cyan]")
                console.print(f"   {result.get('content', '').strip()}")

            if len(results) > 5:
                console.print(f"[dim]... and {len(results) - 5} more matches[/dim]")

            return {"success": True, "data": {"results": results, "query": search_query}}

        except Exception as e:
            logger.error(f"Code search failed: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_codebase_index(self) -> Dict[str, Any]:
        """Perform comprehensive codebase indexing with full AI access"""
        console.print("[yellow]🔍 Starting comprehensive codebase indexing...[/yellow]")

        try:
            if not self.codebase_indexer:
                return {"success": False, "error": "Codebase indexer not available"}

            # Perform full codebase scan with autonomous access
            result = await self.codebase_indexer.full_codebase_scan()

            if result.get("success"):
                console.print("[green]✅ Codebase indexing completed![/green]")

                # Display comprehensive summary
                files_scanned = result.get("files_scanned", 0)
                quality_report = result.get("quality_report", {})
                auto_fixes = result.get("auto_fixes", {})
                index_report = result.get("index_report", {})

                console.print(f"[cyan]📊 Scanned {files_scanned} files[/cyan]")
                console.print(f"[cyan]📈 Quality Score: {quality_report.get('quality_score', 0)}/100[/cyan]")
                console.print(f"[cyan]🔧 Auto-fixes Applied: {auto_fixes.get('fixes_applied', 0)}[/cyan]")
                console.print(f"[cyan]📚 Functions: {index_report.get('total_functions', 0)}[/cyan]")
                console.print(f"[cyan]🏗️ Classes: {index_report.get('total_classes', 0)}[/cyan]")

                if auto_fixes.get("fixes_applied", 0) > 0:
                    console.print("[green]🎉 AI automatically fixed issues in the codebase![/green]")

                return {"success": True, "data": result}
            else:
                return result

        except Exception as e:
            logger.error(f"Codebase indexing failed: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_autonomous_fix(self) -> Dict[str, Any]:
        """Autonomous error detection and fixing"""
        console.print("[yellow]🤖 Running autonomous error detection and fixing...[/yellow]")

        try:
            if not self.codebase_indexer:
                return {"success": False, "error": "Codebase indexer not available"}

            # First, ensure codebase is indexed
            await self.codebase_indexer.full_codebase_scan()

            # Get auto-fixable errors and fix them
            auto_fixes = await self.codebase_indexer._auto_fix_issues()

            fixes_applied = auto_fixes.get("fixes_applied", 0)

            if fixes_applied > 0:
                console.print(f"[green]✅ AI autonomously fixed {fixes_applied} issues![/green]")

                # Display fixes
                for fix in auto_fixes.get("fixes_log", []):
                    console.print(f"[green]  ✓ {fix['file']}:{fix['line']} - {fix['fix']}[/green]")
            else:
                console.print("[cyan]✨ No auto-fixable issues found - codebase is clean![/cyan]")

            return {"success": True, "data": auto_fixes}

        except Exception as e:
            logger.error(f"Autonomous fixing failed: {e}")
            return {"success": False, "error": str(e)}

    def get_file_analysis(self, file_path: str) -> Dict[str, Any]:
        """Get comprehensive analysis of a specific file"""
        try:
            if not self.codebase_indexer:
                return {"success": False, "error": "Codebase indexer not available"}

            file_info = self.codebase_indexer.get_file_info(file_path)

            if file_info:
                return {"success": True, "data": file_info}
            else:
                return {"success": False, "error": f"File not found in index: {file_path}"}

        except Exception as e:
            logger.error(f"File analysis failed: {e}")
            return {"success": False, "error": str(e)}

    def search_codebase_content(self, query: str, file_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Search across the entire codebase content"""
        try:
            if not self.codebase_indexer:
                return {"success": False, "error": "Codebase indexer not available"}

            results = self.codebase_indexer.search_codebase(query, file_types)

            console.print(f"[green]✅ Found {len(results)} files with matches![/green]")

            # Display first few results
            for i, result in enumerate(results[:3]):
                console.print(f"[cyan]{i+1}. {result['file']} ({result['language']})[/cyan]")
                for match in result['matches'][:2]:
                    console.print(f"   Line {match['line']}: {match['content']}")

            if len(results) > 3:
                console.print(f"[dim]... and {len(results) - 3} more files[/dim]")

            return {"success": True, "data": {"results": results, "query": query}}

        except Exception as e:
            logger.error(f"Codebase search failed: {e}")
            return {"success": False, "error": str(e)}

    async def _handle_project_tree(self) -> Dict[str, Any]:
        """Handle /project-tree command"""
        console.print("[yellow]🌳 Displaying project tree...[/yellow]")

        try:
            if not self.project_analyzer:
                return {"success": False, "error": "Project analyzer not available"}

            # Display project tree
            self.project_analyzer.display_project_tree()

            return {"success": True, "message": "Project tree displayed"}

        except Exception as e:
            logger.error(f"Project tree display failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_fix_history(self) -> Dict[str, Any]:
        """Handle /fix-history command"""
        console.print("[yellow]📜 Displaying fix history...[/yellow]")

        try:
            if not self.self_fixer:
                return {"success": False, "error": "Self-fixer not available"}

            history = await self.self_fixer.get_fix_history()

            if not history:
                console.print("[cyan]📜 No fix history available[/cyan]")
                return {"success": True, "message": "No fix history"}

            console.print(f"[cyan]📜 Found {len(history)} fix attempts:[/cyan]")

            for i, fix in enumerate(history[-10:], 1):  # Show last 10
                status = "✅" if fix.get("fix_successful") else "❌"
                console.print(f"{status} {i}. {fix.get('error_type', 'unknown')} - {fix.get('timestamp', '')}")

            return {"success": True, "data": {"history": history}}

        except Exception as e:
            logger.error(f"Fix history display failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_quality_check(self, file_path: str) -> Dict[str, Any]:
        """Handle /quality-check command"""
        if not file_path:
            return {"success": False, "error": "Please specify a file path"}

        console.print(f"[yellow]🔍 Running quality check on: {file_path}[/yellow]")

        try:
            if not self.code_analyzer:
                return {"success": False, "error": "Code analyzer not available"}

            result = await self.code_analyzer.run_code_quality_check(file_path)

            if result.get("success"):
                quality_report = result.get("quality_report", {})
                score = quality_report.get("quality_score", 0)

                score_color = "green" if score >= 80 else "yellow" if score >= 60 else "red"
                console.print(f"[{score_color}]📊 Quality Score: {score:.1f}/100[/{score_color}]")

                metrics = quality_report.get("metrics", {})
                console.print(f"[cyan]📈 Metrics: {metrics.get('lines_of_code', 0)} lines, "
                            f"{metrics.get('functions', 0)} functions, "
                            f"{metrics.get('issues', 0)} issues[/cyan]")

                return result
            else:
                return result

        except Exception as e:
            logger.error(f"Quality check failed: {e}", exc_info=True)
            return {"success": False, "error": str(e)}

    async def _handle_help(self) -> Dict[str, Any]:
        """Handle /help command"""
        console.print("[cyan]🤖 sMTm Enhanced AI Commands:[/cyan]")
        console.print("")

        # Original commands
        console.print("[yellow]📱 Mobile Testing Commands:[/yellow]")
        console.print("  [green]/ai-analysis[/green]      - Autonomous mobile app analysis")
        console.print("  [green]/generate-gherkin[/green] - Generate BDD test scenarios from analysis")
        console.print("  [green]/generate-gherkin [instructions][/green] - Generate custom test scenarios")
        console.print("  [green]/mobile-test[/green]      - Execute automated mobile tests")
        console.print("")

        # Learning commands
        console.print("[yellow]🧠 AI Learning Commands:[/yellow]")
        console.print("  [green]/self-learning[/green] [topic]   - Learn about a specific topic")
        console.print("  [green]/multi-learning[/green] [topics] - Learn multiple topics")
        console.print("")

        # Enhanced commands
        console.print("[yellow]🔍 Project Analysis Commands:[/yellow]")
        console.print("  [green]/analyze-project[/green]         - Comprehensive project analysis")
        console.print("  [green]/analyze-file[/green] [path]     - Analyze specific code file")
        console.print("  [green]/search-code[/green] [query]     - Search code across project")
        console.print("  [green]/project-tree[/green]            - Display project structure")
        console.print("  [green]/quality-check[/green] [path]    - Run code quality analysis")
        console.print("")

        console.print("[yellow]🔧 Self-Fixing Commands:[/yellow]")
        console.print("  [green]/health-check[/green]            - Check project health")
        console.print("  [green]/self-diagnose[/green]           - Comprehensive self-diagnosis")
        console.print("  [green]/fix-error[/green] [description] - Attempt to fix described error")
        console.print("  [green]/fix-history[/green]             - Show fix attempt history")
        console.print("")

        console.print("[yellow]⚙️ Code Modification Commands:[/yellow]")
        console.print("  [green]/modify-code[/green] [request]   - Request code modifications")
        console.print("")

        console.print("[yellow]ℹ️ General Commands:[/yellow]")
        console.print("  [green]/help[/green]                    - Show this help message")
        console.print("")

        console.print("[cyan]💡 The AI has full project access and self-fixing capabilities![/cyan]")

        return {"success": True, "message": "Help displayed"}

    async def generate_response(self, prompt: str, intent: str = "general") -> str:
        """Generate AI response using Ollama with performance optimization for 1-5 second responses"""
        start_time = time.time()

        try:
            if not self.is_initialized:
                return "AI Engine not initialized"

            # Check cache first for instant responses (use longer TTL for knowledge responses)
            cache_ttl = self.config.get("KNOWLEDGE_CACHE_TTL") if intent in ["knowledge_synthesis", "general_knowledge"] else None
            cached_response = self.performance_optimizer.get_cached_response(prompt, cache_ttl)
            if cached_response:
                response_time = time.time() - start_time
                is_comprehensive = intent == "knowledge_inquiry"
                self.performance_optimizer.track_response_time(response_time, is_comprehensive)
                logger.debug(f"Cache hit - Response time: {response_time:.2f}s")
                return cached_response

            # Optimize prompt for faster generation
            optimized_prompt = self.performance_optimizer.optimize_prompt(prompt, intent)

            # Get optimized Ollama options for faster responses (use optimized_prompt for token allocation)
            optimized_options = self.performance_optimizer.get_optimized_ollama_options(optimized_prompt)

            # Prepare optimized request for Ollama (no streaming for better reliability)
            payload = {
                "model": self.model_name,
                "prompt": optimized_prompt,
                "stream": False,
                "options": optimized_options
            }

            # Choose timeout based on intent
            timeout = self._get_timeout_for_intent(intent)

            # Send request to Ollama with optimized timeout
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=payload,
                timeout=timeout
            )

            if response.status_code == 200:
                result = response.json()
                ai_response = result.get("response", "No response generated")
            else:
                error_msg = f"Ollama API error: {response.status_code}"
                logger.error(error_msg)
                return f"Error: {error_msg}"

            # Cache the response for future use
            self.performance_optimizer.cache_response(prompt, ai_response)

            # Track performance (mark as comprehensive for knowledge_inquiry intent)
            response_time = time.time() - start_time
            is_comprehensive = intent == "knowledge_inquiry"
            self.performance_optimizer.track_response_time(response_time, is_comprehensive)
            logger.debug(f"Generated response in {response_time:.2f}s")

            return ai_response

        except requests.exceptions.Timeout:
            error_msg = "Request to Ollama timed out"
            logger.error(error_msg)
            response_time = time.time() - start_time
            is_comprehensive = intent == "knowledge_inquiry"
            self.performance_optimizer.track_response_time(response_time, is_comprehensive)
            return f"Error: {error_msg}"
        except Exception as e:
            logger.error(f"Response generation failed: {e}", exc_info=True)
            response_time = time.time() - start_time
            is_comprehensive = intent == "knowledge_inquiry"
            self.performance_optimizer.track_response_time(response_time, is_comprehensive)
            return f"Error generating response: {e}"

    def _get_timeout_for_intent(self, intent: str) -> int:
        """Get appropriate timeout based on intent"""
        if intent in ["knowledge_synthesis", "general_knowledge"]:
            return self.config.get("OLLAMA_KNOWLEDGE_TIMEOUT", 10)
        elif intent in ["fast_response", "quick_answer"]:
            return self.config.get("OLLAMA_FAST_TIMEOUT", 5)  # Very fast for immediate responses
        elif intent == "knowledge_inquiry":
            return self.config.get("OLLAMA_COMPREHENSIVE_TIMEOUT", 180)  # Extended timeout for comprehensive responses (3 minutes)
        else:
            return self.config.get("OLLAMA_TIMEOUT", 30)

    async def _handle_streaming_response(self, payload: Dict[str, Any], timeout: int, start_time: float) -> str:
        """Handle streaming response from Ollama for faster comprehensive responses"""
        import aiohttp
        import asyncio

        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
                async with session.post(
                    f"{self.ollama_url}/api/generate",
                    json=payload
                ) as response:
                    if response.status != 200:
                        error_msg = f"Ollama API error: {response.status}"
                        logger.error(error_msg)
                        return f"Error: {error_msg}"

                    full_response = ""
                    async for line in response.content:
                        if line:
                            try:
                                import json
                                chunk = json.loads(line.decode('utf-8'))
                                if 'response' in chunk:
                                    full_response += chunk['response']
                                if chunk.get('done', False):
                                    break
                            except json.JSONDecodeError:
                                continue

                    return full_response

        except asyncio.TimeoutError:
            error_msg = "Streaming request to Ollama timed out"
            logger.error(error_msg)
            return f"Error: {error_msg}"
        except Exception as e:
            logger.error(f"Streaming response failed: {e}")
            return f"Error: {e}"
