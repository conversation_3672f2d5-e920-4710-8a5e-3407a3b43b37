"""
AI Performance Optimizer - Achieve 1-5 second response times
Implements caching, prompt optimization, and concurrent processing
"""

import asyncio
import hashlib
import time
import json
from typing import Dict, Any, Optional, List
from loguru import logger
import threading
from concurrent.futures import ThreadPoolExecutor
from utils.config import Config

class PerformanceOptimizer:
    """Optimize AI response performance for 1-5 second response times"""
    
    def __init__(self, config: Config):
        self.config = config
        self.response_cache = {}
        self.cache_timestamps = {}
        self.cache_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # Performance settings
        self.cache_enabled = config.get("RESPONSE_CACHE_ENABLED", True)
        self.cache_ttl = config.get("RESPONSE_CACHE_TTL", 300)  # 5 minutes
        self.fast_mode = config.get("FAST_RESPONSE_MODE", True)
        self.prompt_optimization = config.get("PROMPT_OPTIMIZATION", True)
        
        # Response time tracking
        self.response_times = []
        self.target_response_time = 3.0  # Target 3 seconds average
        self.comprehensive_target_time = 60.0  # Target 1 minute for comprehensive responses
        
        logger.info("Performance Optimizer initialized for 1-5 second responses")
    
    def _generate_cache_key(self, prompt: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Generate cache key for prompt and context"""
        cache_data = {
            "prompt": prompt.strip().lower(),
            "context": context or {}
        }
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_key: str, custom_ttl: Optional[int] = None) -> bool:
        """Check if cached response is still valid"""
        if not self.cache_enabled or cache_key not in self.cache_timestamps:
            return False

        age = time.time() - self.cache_timestamps[cache_key]
        ttl_to_use = custom_ttl if custom_ttl is not None else self.cache_ttl
        return age < ttl_to_use

    def get_cached_response(self, prompt: str, context: Optional[Dict[str, Any]] = None, custom_ttl: Optional[int] = None) -> Optional[str]:
        """Get cached response if available and valid"""
        if not self.cache_enabled:
            return None

        cache_key = self._generate_cache_key(prompt, context)

        with self.cache_lock:
            if self._is_cache_valid(cache_key, custom_ttl):
                logger.debug(f"Cache hit for prompt: {prompt[:50]}...")
                return self.response_cache[cache_key]

        return None
    
    def cache_response(self, prompt: str, response: str, context: Optional[Dict[str, Any]] = None):
        """Cache response for future use"""
        if not self.cache_enabled:
            return
        
        cache_key = self._generate_cache_key(prompt, context)
        
        with self.cache_lock:
            self.response_cache[cache_key] = response
            self.cache_timestamps[cache_key] = time.time()
            
            # Clean old cache entries if cache is getting large
            if len(self.response_cache) > 100:
                self._cleanup_cache()
    
    def _cleanup_cache(self):
        """Remove expired cache entries"""
        current_time = time.time()
        expired_keys = [
            key for key, timestamp in self.cache_timestamps.items()
            if current_time - timestamp > self.cache_ttl
        ]
        
        for key in expired_keys:
            self.response_cache.pop(key, None)
            self.cache_timestamps.pop(key, None)
        
        logger.debug(f"Cleaned {len(expired_keys)} expired cache entries")
    
    def optimize_prompt(self, prompt: str, intent: str = "general") -> str:
        """Optimize prompt for faster response generation while respecting natural conversation mode"""
        # Check if natural conversation mode is enabled
        natural_mode = self.config.get("NATURAL_CONVERSATION_MODE", True)
        prompt_optimization = self.config.get("PROMPT_OPTIMIZATION", "natural")

        # If natural conversation mode is enabled or prompt optimization is disabled, return original prompt
        if natural_mode and prompt_optimization == "natural":
            # Only apply very minimal optimizations that don't change conversational tone
            return prompt
        elif not self.prompt_optimization or prompt_optimization is False:
            return prompt

        # Fast mode optimizations (only if not in natural conversation mode)
        if self.fast_mode and not natural_mode:
            optimized_prompt = self._apply_fast_mode_optimizations(prompt, intent)
        else:
            optimized_prompt = prompt

        return optimized_prompt
    
    def _apply_fast_mode_optimizations(self, prompt: str, intent: str) -> str:
        """Apply fast mode optimizations to prompt while maintaining natural conversational tone"""

        # Check if this is a detailed request that shouldn't be optimized
        prompt_lower = prompt.lower()
        detail_indicators = [
            "with detail", "in detail", "detailed", "comprehensive", "thorough",
            "explain everything", "tell me everything", "full explanation",
            "complete explanation", "elaborate", "extensively", "in depth"
        ]

        is_detail_request = any(indicator in prompt_lower for indicator in detail_indicators)

        # For natural conversation, avoid rigid prompt wrapping
        # Instead, use subtle optimizations that preserve conversational tone

        if intent == "knowledge_inquiry":
            if is_detail_request:
                # Optimize comprehensive requests for speed while maintaining quality
                return self._optimize_comprehensive_prompt(prompt)
            else:
                # Keep the original prompt but add a subtle hint for conciseness
                return f"{prompt}\n\n(Please keep your response helpful but concise)"

        elif intent == "code_analysis":
            # Preserve natural tone while hinting at focus
            return f"{prompt}\n\n(Focus on the key technical points)"

        elif intent == "project_help":
            # Keep conversational while encouraging actionable responses
            return f"{prompt}\n\n(Please provide practical, actionable guidance)"

        else:
            # General optimization - minimal intervention for natural conversation
            if is_detail_request:
                # Don't optimize detailed requests at all
                return prompt
            else:
                # Very subtle optimization that doesn't change conversational tone
                return prompt

    def _optimize_detailed_prompt(self, prompt: str) -> str:
        """Optimize detailed prompts for faster generation while maintaining natural conversational quality"""
        # For detailed requests, preserve the natural conversational tone
        # but add a subtle structure hint to help with faster generation

        prompt_lower = prompt.lower()

        # Check if the user is asking for a specific structured format
        if any(word in prompt_lower for word in ["steps", "list", "points", "structure", "format"]):
            # User wants structure, so keep their request as-is
            return prompt

        # For general detailed requests, add a gentle suggestion for organization
        # without being rigid or formal
        return f"{prompt}\n\n(Feel free to organize your response in a clear, structured way that covers the key aspects comprehensively)"

    def _optimize_comprehensive_prompt(self, prompt: str) -> str:
        """Optimize comprehensive prompts for faster generation while maintaining quality"""
        # Add speed optimization hints for comprehensive requests
        return f"{prompt}\n\nPlease provide a comprehensive response that is well-structured and covers all key aspects efficiently. Focus on delivering maximum value while maintaining clarity and completeness."

    def track_response_time(self, response_time: float, is_comprehensive: bool = False):
        """Track response time for performance monitoring"""
        self.response_times.append(response_time)

        # Keep only last 50 response times
        if len(self.response_times) > 50:
            self.response_times = self.response_times[-50:]

        # Log if response is slower than target (use different thresholds for comprehensive vs regular)
        target_time = self.comprehensive_target_time if is_comprehensive else self.target_response_time
        if response_time > target_time:
            response_type = "comprehensive" if is_comprehensive else "regular"
            logger.warning(f"Slow {response_type} response: {response_time:.2f}s (target: {target_time}s)")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        if not self.response_times:
            return {
                "average_response_time": 0,
                "cache_hit_rate": 0,
                "cache_size": len(self.response_cache)
            }
        
        avg_time = sum(self.response_times) / len(self.response_times)
        fast_responses = sum(1 for t in self.response_times if t <= 5.0)
        fast_rate = (fast_responses / len(self.response_times)) * 100
        
        return {
            "average_response_time": round(avg_time, 2),
            "fast_response_rate": round(fast_rate, 1),
            "cache_size": len(self.response_cache),
            "total_responses": len(self.response_times),
            "target_met": avg_time <= self.target_response_time
        }
    
    async def optimize_concurrent_processing(self, tasks: List[Any]) -> List[Any]:
        """Process multiple tasks concurrently for better performance"""
        if not self.config.get("CONCURRENT_PROCESSING", True):
            # Sequential processing fallback
            results = []
            for task in tasks:
                result = await task
                results.append(result)
            return results
        
        # Concurrent processing
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return results
        except Exception as e:
            logger.error(f"Concurrent processing failed: {e}")
            # Fallback to sequential
            results = []
            for task in tasks:
                try:
                    result = await task
                    results.append(result)
                except Exception as task_error:
                    logger.error(f"Task failed: {task_error}")
                    results.append(None)
            return results
    
    def should_use_fast_mode(self, prompt: str) -> bool:
        """Determine if fast mode should be used for this prompt"""
        if not self.fast_mode:
            return False

        prompt_lower = prompt.lower()

        # Check for explicit detail requests - these should NOT use fast mode
        detail_indicators = [
            "with detail", "in detail", "detailed", "comprehensive", "thorough",
            "explain everything", "tell me everything", "full explanation",
            "complete explanation", "elaborate", "extensively", "in depth"
        ]

        if any(indicator in prompt_lower for indicator in detail_indicators):
            return False

        # Use fast mode for simple queries
        fast_mode_indicators = [
            "what is", "how to", "define", "help with",
            "show me", "tell me", "quick", "simple", "brief"
        ]

        # Don't use fast mode for "explain" if it's not explicitly brief
        if "explain" in prompt_lower and not any(brief in prompt_lower for brief in ["quick", "brief", "simple"]):
            return False

        return any(indicator in prompt_lower for indicator in fast_mode_indicators)

    def _is_comprehensive_request(self, prompt: str) -> bool:
        """Check if this is a comprehensive request that needs more resources"""
        prompt_lower = prompt.lower()
        comprehensive_indicators = [
            "comprehensive", "detailed tutorial", "complete guide", "thorough",
            "in depth", "step by step", "full tutorial", "detailed explanation",
            "complete analysis", "extensive", "elaborate"
        ]
        return any(indicator in prompt_lower for indicator in comprehensive_indicators)

    def _get_gherkin_optimized_options(self, prompt: str) -> Dict[str, Any]:
        """Get optimized Ollama options specifically for Gherkin generation"""
        # Optimized parameters for fast, structured Gherkin generation
        gherkin_options = {
            "temperature": 0.3,  # Lower temperature for more consistent, structured output
            "num_predict": 200,  # Smaller token limit for focused Gherkin scenarios
            "top_p": 0.7,       # More focused vocabulary for structured output
            "top_k": 20,        # Reduced vocabulary range for consistency
            "repeat_penalty": 1.05,  # Light penalty to avoid repetition
            "stop": ["---", "Feature:", "Background:"],  # Stop tokens for Gherkin structure
        }

        print(f"🎯 Gherkin Optimized: {gherkin_options['num_predict']} tokens (Structured output)")
        logger.debug(f"Gherkin optimization applied: {gherkin_options}")
        return gherkin_options

    def get_optimized_ollama_options(self, prompt: str, intent: Optional[str] = None) -> Dict[str, Any]:
        """Get optimized Ollama options with smart dynamic token allocation (Claude-level capacity)"""
        # Special optimization for Gherkin generation
        if intent == "gherkin_generation":
            return self._get_gherkin_optimized_options(prompt)

        # Determine if this is a comprehensive request
        is_comprehensive = self._is_comprehensive_request(prompt)

        # Use same parameters as Augment Code AI for maximum natural conversation
        base_options = {
            "temperature": self.config.get("AI_TEMPERATURE", 1.0),  # Same as Augment Code AI
            "num_predict": self._get_smart_token_limit(prompt),  # Dynamic token allocation
            "top_p": 0.9,  # Keep high for natural language variety
            "top_k": 40,   # Maintain good vocabulary range
            "repeat_penalty": 1.1,  # Prevent repetitive text
        }

        # Add performance optimizations for comprehensive requests
        if is_comprehensive:
            base_options.update({
                "num_ctx": 4096,   # Optimized context window for comprehensive responses
                "num_batch": 2048, # Very large batch size for maximum speed
                "num_thread": -1,  # Use all available CPU threads
                "num_gpu": -1,     # Use all available GPU layers
                "top_p": 0.9,      # Balanced for speed and quality
                "mirostat": 2,     # Enable Mirostat v2 for better quality/speed balance
                "mirostat_tau": 4.0,  # Optimized target entropy for speed
                "mirostat_eta": 0.2,  # Higher learning rate for faster convergence
                "use_mmap": True,     # Memory mapping for faster model loading
                "use_mlock": True,    # Lock model in memory for speed
                "low_vram": False,    # Disable VRAM optimization for speed
                "f16_kv": True,       # Use FP16 for key-value cache (faster)
                "logits_all": False,  # Don't compute logits for all tokens (faster)
                "vocab_only": False,  # Don't load vocab only (we need full model)
                "embedding_only": False,  # Don't use embedding only mode
            })
        else:
            base_options.update({
                "num_ctx": 2048,  # Smaller context for faster responses
                "num_batch": 1024,  # Large batch for quick responses
                "num_thread": -1,   # Use all available CPU threads
                "num_gpu": -1,     # Use all available GPU layers
                "f16_kv": True,    # Use FP16 for speed
            })

        return base_options

    def _get_smart_token_limit(self, prompt: str) -> int:
        """Smart dynamic token allocation based on request complexity and type"""
        prompt_lower = prompt.lower()

        # Debug: Log the prompt being analyzed for token allocation
        logger.debug(f"Token allocation analyzing prompt: {prompt[:100]}...")

        # Analyze prompt complexity and intent
        complexity_score = self._analyze_prompt_complexity(prompt_lower)

        # Quick responses (simple questions, greetings, confirmations)
        quick_indicators = [
            "what is", "who is", "when", "where", "yes", "no", "thanks", "hello",
            "hi", "ok", "sure", "got it", "understood", "quick", "brief", "short"
        ]

        # Comprehensive analysis requests (project analysis, code review, etc.)
        comprehensive_indicators = [
            "analyze project", "analyze file", "comprehensive", "complete analysis",
            "full review", "entire codebase", "all features", "everything about",
            "thorough analysis", "deep dive", "extensive", "exhaustive",
            "comprehensive tutorial", "detailed tutorial", "complete tutorial",
            "tutorial", "guide", "comprehensive guide", "detailed guide"
        ]

        # Detailed explanation requests
        detailed_indicators = [
            "explain in detail", "detailed explanation", "with detail", "elaborate",
            "tell me everything", "full explanation", "comprehensive guide",
            "step by step", "how does it work", "architecture", "implementation"
        ]

        # Code-related requests (often need more space for examples)
        code_indicators = [
            "code", "function", "class", "method", "implementation", "example",
            "syntax", "programming", "script", "algorithm", "debug", "fix"
        ]

        # Determine appropriate token limit with reasoning
        token_limit = None
        reasoning = ""

        # Use word boundary matching to avoid false positives
        import re

        def matches_whole_words(text, indicators):
            """Check if any indicator matches as whole words in the text"""
            for indicator in indicators:
                # Create pattern that matches the indicator as whole words
                pattern = r'\b' + re.escape(indicator) + r'\b'
                if re.search(pattern, text, re.IGNORECASE):
                    return True
            return False

        def get_matched_indicators(text, indicators):
            """Get list of indicators that match as whole words"""
            matched = []
            for indicator in indicators:
                pattern = r'\b' + re.escape(indicator) + r'\b'
                if re.search(pattern, text, re.IGNORECASE):
                    matched.append(indicator)
            return matched

        # Check comprehensive indicators FIRST (higher priority)
        if any(indicator in prompt_lower for indicator in comprehensive_indicators):
            # Debug: Log which comprehensive indicator was matched
            matched_indicators = [indicator for indicator in comprehensive_indicators if indicator in prompt_lower]
            logger.debug(f"Comprehensive indicators matched: {matched_indicators}")
            # Use a more practical token limit for comprehensive responses (2048 instead of 4096)
            # This provides detailed responses while avoiding timeout issues
            token_limit = self.config.get("AI_NUM_PREDICT_COMPREHENSIVE", 2048)
            reasoning = "Comprehensive analysis (Detailed)"

        elif matches_whole_words(prompt_lower, quick_indicators):
            # Debug: Log which quick indicator was matched
            matched_quick = get_matched_indicators(prompt_lower, quick_indicators)
            logger.debug(f"Quick indicators matched: {matched_quick}")
            token_limit = self.config.get("AI_NUM_PREDICT_QUICK", 256)
            reasoning = "Quick response"

        elif any(indicator in prompt_lower for indicator in detailed_indicators):
            token_limit = self.config.get("AI_NUM_PREDICT_DETAILED", 1024)
            reasoning = "Detailed explanation"

        elif any(indicator in prompt_lower for indicator in code_indicators):
            token_limit = self.config.get("AI_NUM_PREDICT_COMPREHENSIVE", 2048)
            reasoning = "Code-related request"

        elif complexity_score > 0.7:
            token_limit = self.config.get("AI_NUM_PREDICT_DETAILED", 1024)
            reasoning = f"High complexity (score: {complexity_score:.2f})"

        elif complexity_score > 0.4:
            token_limit = self.config.get("AI_NUM_PREDICT_STANDARD", 512)
            reasoning = f"Medium complexity (score: {complexity_score:.2f})"

        else:
            token_limit = self.config.get("AI_NUM_PREDICT_QUICK", 256)
            reasoning = f"Simple request (score: {complexity_score:.2f})"

        # Log the smart allocation decision (visible in console for testing)
        print(f"🎯 Smart Token Allocation: {token_limit} tokens ({reasoning})")
        logger.debug(f"Smart token allocation: {token_limit} tokens ({reasoning}) for prompt: {prompt[:50]}...")

        return token_limit

    def _analyze_prompt_complexity(self, prompt_lower: str) -> float:
        """Analyze prompt complexity to determine appropriate response length"""
        complexity_score = 0.0

        # Length factor (longer prompts often need longer responses)
        word_count = len(prompt_lower.split())
        if word_count > 20:
            complexity_score += 0.3
        elif word_count > 10:
            complexity_score += 0.2
        elif word_count > 5:
            complexity_score += 0.1

        # Technical complexity indicators
        technical_keywords = [
            "architecture", "implementation", "algorithm", "framework", "system",
            "integration", "configuration", "optimization", "performance", "security",
            "database", "api", "protocol", "methodology", "best practices"
        ]

        technical_count = sum(1 for keyword in technical_keywords if keyword in prompt_lower)
        complexity_score += min(technical_count * 0.1, 0.4)  # Cap at 0.4

        # Question complexity (multiple questions or complex queries)
        question_indicators = ["how", "why", "what", "when", "where", "which", "explain"]
        question_count = sum(1 for indicator in question_indicators if indicator in prompt_lower)
        if question_count > 2:
            complexity_score += 0.2
        elif question_count > 1:
            complexity_score += 0.1

        return min(complexity_score, 1.0)  # Cap at 1.0
