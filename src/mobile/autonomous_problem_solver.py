"""
Autonomous Problem Solver for AI Analysis
Monitors analysis in real-time, detects problems, researches solutions, and implements fixes automatically
"""

import asyncio
import time
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

from utils.config import Config
from utils.console import get_centralized_console
from utils.terminal_logger import log_ai_analysis_event, log_error_with_context
from .autonomous_config import AutonomousConfig

console = get_centralized_console()

class AutonomousProblemSolver:
    """
    Autonomous problem solver that monitors AI analysis, detects issues,
    researches solutions on the internet, and implements fixes automatically
    """
    
    def __init__(self, web_search_tool=None, web_fetch_tool=None, config_file=None):
        self.config = Config()
        self.autonomous_config = AutonomousConfig(config_file)
        self.web_search = web_search_tool
        self.web_fetch = web_fetch_tool
        
        # Problem detection and solving state
        self.is_monitoring = False
        self.analysis_context = {}
        self.detected_problems = []
        self.solved_problems = []
        self.failed_solutions = []
        
        # Load problem patterns from configuration
        self.problem_patterns = self.autonomous_config.get_problem_patterns()
        
        # Solution implementation strategies
        self.solution_strategies = {
            "configuration_change": self._implement_config_change,
            "code_modification": self._implement_code_modification,
            "retry_mechanism": self._implement_retry_mechanism,
            "timeout_adjustment": self._implement_timeout_adjustment,
            "detection_strategy": self._implement_detection_strategy
        }
        
        # Load monitoring configuration from enhanced config
        self.monitoring_config = self.autonomous_config.get_monitoring_config()
        
        # Initialize problem analyzer and solution implementer
        self.problem_analyzer = None
        self.solution_implementer = None
        
    async def initialize(self):
        """Initialize the autonomous problem solver"""
        try:
            console.print("[cyan]🤖 Initializing Autonomous Problem Solver...[/cyan]")
            
            # Initialize problem analyzer
            from .problem_analyzer import ProblemAnalyzer
            self.problem_analyzer = ProblemAnalyzer(self.web_search, self.web_fetch)
            await self.problem_analyzer.initialize()
            
            # Initialize solution implementer
            from .solution_implementer import SolutionImplementer
            self.solution_implementer = SolutionImplementer()
            await self.solution_implementer.initialize()
            
            console.print("[green]✅ Autonomous Problem Solver initialized successfully[/green]")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Autonomous Problem Solver: {e}")
            console.print(f"[red]❌ Failed to initialize Autonomous Problem Solver: {e}[/red]")
            return False
    
    async def start_monitoring(self, analysis_context: Dict[str, Any]):
        """Start monitoring the AI analysis for problems"""
        try:
            self.analysis_context = analysis_context
            self.is_monitoring = True
            
            console.print("[cyan]🔍 Starting autonomous problem monitoring...[/cyan]")
            log_ai_analysis_event("autonomous_monitoring_started",
                "Autonomous monitoring started with background mode enabled")
            
            # Start monitoring loop in background
            if self.monitoring_config["background_mode"]:
                asyncio.create_task(self._monitoring_loop())
            else:
                await self._monitoring_loop()
                
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")
            log_error_with_context(e, "monitoring_start_failed", {"analysis_context": str(analysis_context)})
    
    async def stop_monitoring(self):
        """Stop monitoring the AI analysis"""
        self.is_monitoring = False
        console.print("[yellow]⏹️ Stopped autonomous problem monitoring[/yellow]")
        log_ai_analysis_event("autonomous_monitoring_stopped",
            f"Problems detected: {len(self.detected_problems)}, solved: {len(self.solved_problems)}, failed: {len(self.failed_solutions)}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop that runs in background"""
        try:
            while self.is_monitoring:
                # Check for problems
                await self._detect_and_analyze_problems()
                
                # Wait before next check
                await asyncio.sleep(self.monitoring_config["check_interval_seconds"])
                
        except Exception as e:
            logger.error(f"Monitoring loop failed: {e}")
            log_error_with_context(e, "monitoring_loop_failed", {"analysis_context": str(self.analysis_context)})
    
    async def _detect_and_analyze_problems(self):
        """Detect and analyze problems in the current analysis"""
        try:
            # Get current analysis metrics
            current_problems = await self._detect_current_problems()
            
            for problem in current_problems:
                if not self._is_problem_already_detected(problem):
                    console.print(f"[yellow]🚨 NEW PROBLEM DETECTED: {problem['type']}[/yellow]")
                    console.print(f"[yellow]📝 Description: {problem['description']}[/yellow]")
                    
                    # Add to detected problems
                    self.detected_problems.append(problem)
                    
                    # Analyze and solve problem in background
                    if self.monitoring_config["background_mode"]:
                        asyncio.create_task(self._analyze_and_solve_problem(problem))
                    else:
                        await self._analyze_and_solve_problem(problem)
                        
        except Exception as e:
            logger.error(f"Problem detection failed: {e}")
    
    def _is_problem_already_detected(self, problem: Dict[str, Any]) -> bool:
        """Check if problem was already detected"""
        problem_signature = f"{problem['type']}_{problem.get('signature', '')}"

        for detected in self.detected_problems:
            detected_signature = f"{detected['type']}_{detected.get('signature', '')}"
            if problem_signature == detected_signature:
                return True
        return False

    async def _detect_current_problems(self):
        # Preserve sMTm's adaptive threshold
        current_threshold = self.calculate_adaptive_threshold()
        
        # Add BACKUP's severity logic
        if elements_found < current_threshold * 0.5:
            problem['severity'] = 'critical'
        elif elements_found < current_threshold:
            problem['severity'] = 'high'
        problems = []

        try:
            # Get current analysis metrics
            metrics = self.analysis_context.get("metrics", {})

            # Problem 1: Low element detection
            elements_found = metrics.get("elements_found", 0)
            target_elements = self.autonomous_config.get("targets.target_elements_count", 3000)
            low_threshold = self.autonomous_config.get("monitoring.alert_thresholds.low_elements_threshold", 1500)
            critical_threshold = self.autonomous_config.get("monitoring.alert_thresholds.critical_low_elements", 900)

            if elements_found > 0:
                severity = "critical" if elements_found < critical_threshold else "high" if elements_found < low_threshold else None

                if severity:
                    problems.append({
                        "id": f"low_elements_{int(time.time())}",
                        "type": "low_element_detection",
                        "severity": severity,
                        "description": f"Found only {elements_found} elements, target is {target_elements}",
                        "metrics": {"found": elements_found, "target": target_elements, "threshold": low_threshold},
                        "signature": f"elements_{elements_found}",
                        "timestamp": datetime.now().isoformat()
                    })

            # Problem 2: High unknown elements percentage
            unknown_elements = metrics.get("unknown_elements", 0)
            if elements_found > 0:
                unknown_percentage = (unknown_elements / elements_found) * 100
                high_unknown_threshold = self.autonomous_config.get("monitoring.alert_thresholds.high_unknown_percent", 3.0)
                critical_unknown_threshold = self.autonomous_config.get("monitoring.alert_thresholds.critical_unknown_percent", 10.0)

                severity = "critical" if unknown_percentage > critical_unknown_threshold else "medium" if unknown_percentage > high_unknown_threshold else None

                if severity:
                    problems.append({
                        "id": f"high_unknown_{int(time.time())}",
                        "type": "unknown_elements",
                        "severity": severity,
                        "description": f"Unknown elements: {unknown_percentage:.1f}%, threshold: {high_unknown_threshold}%",
                        "metrics": {"unknown_count": unknown_elements, "percentage": unknown_percentage, "threshold": high_unknown_threshold},
                        "signature": f"unknown_{unknown_percentage:.1f}",
                        "timestamp": datetime.now().isoformat()
                    })

            # Problem 3: Analysis timeout
            analysis_duration = metrics.get("duration_minutes", 0)
            max_duration = self.autonomous_config.get("monitoring.alert_thresholds.max_duration_minutes", 40)
            critical_duration = self.autonomous_config.get("monitoring.alert_thresholds.critical_duration_minutes", 60)

            severity = "critical" if analysis_duration > critical_duration else "high" if analysis_duration > max_duration else None

            if severity:
                problems.append({
                    "id": f"timeout_{int(time.time())}",
                    "type": "analysis_timeout",
                    "severity": severity,
                    "description": f"Analysis duration: {analysis_duration:.1f}min, threshold: {max_duration}min",
                    "metrics": {"duration": analysis_duration, "max_duration": max_duration, "critical_duration": critical_duration},
                    "signature": f"timeout_{analysis_duration:.1f}",
                    "timestamp": datetime.now().isoformat()
                })

            # Problem 4: Device connection issues
            device_status = metrics.get("device_status", "unknown")
            if device_status in ["disconnected", "error", "unresponsive"]:
                problems.append({
                    "id": f"device_{int(time.time())}",
                    "type": "device_connection_issues",
                    "severity": "critical",
                    "description": f"Device status: {device_status}",
                    "metrics": {"device_status": device_status},
                    "signature": f"device_{device_status}",
                    "timestamp": datetime.now().isoformat()
                })

            # Problem 5: Navigation failures
            navigation_failures = metrics.get("navigation_failures", 0)
            max_nav_failures = self.autonomous_config.get("monitoring.alert_thresholds.max_navigation_failures", 5)
            critical_nav_failures = self.autonomous_config.get("monitoring.alert_thresholds.critical_navigation_failures", 15)

            severity = "critical" if navigation_failures > critical_nav_failures else "medium" if navigation_failures > max_nav_failures else None

            if severity:
                problems.append({
                    "id": f"nav_fail_{int(time.time())}",
                    "type": "navigation_failures",
                    "severity": severity,
                    "description": f"Navigation failures: {navigation_failures}, threshold: {max_nav_failures}",
                    "metrics": {"failures": navigation_failures, "max_failures": max_nav_failures, "critical_failures": critical_nav_failures},
                    "signature": f"nav_{navigation_failures}",
                    "timestamp": datetime.now().isoformat()
                })

        except Exception as e:
            logger.error(f"Problem detection failed: {e}")

        return problems

    async def _analyze_and_solve_problem(self, problem: Dict[str, Any]):
        """Analyze a problem and attempt to solve it autonomously"""
        try:
            console.print(f"[cyan]🔬 ANALYZING PROBLEM: {problem['type']}[/cyan]")

            # Step 1: Analyze the problem using AI
            analysis_result = await self.problem_analyzer.analyze_problem(problem)

            if not analysis_result["success"]:
                console.print(f"[red]❌ Problem analysis failed: {analysis_result.get('error')}[/red]")
                return

            # Step 2: Research solutions on the internet
            console.print(f"[cyan]🌐 RESEARCHING SOLUTIONS for {problem['type']}...[/cyan]")
            solutions = await self._research_solutions(problem, analysis_result)

            if not solutions:
                console.print(f"[yellow]⚠️ No solutions found for {problem['type']}[/yellow]")
                return

            # Step 3: Implement the best solution
            console.print(f"[cyan]🔧 IMPLEMENTING SOLUTION for {problem['type']}...[/cyan]")
            implementation_result = await self._implement_best_solution(problem, solutions)

            if implementation_result["success"]:
                console.print(f"[green]✅ PROBLEM SOLVED: {problem['type']}[/green]")
                self.solved_problems.append({
                    "problem": problem,
                    "solution": implementation_result["solution"],
                    "timestamp": datetime.now().isoformat()
                })

                # Log successful solution
                log_ai_analysis_event("problem_solved_autonomously",
                    f"Problem {problem['type']} solved autonomously")
            else:
                console.print(f"[red]❌ SOLUTION FAILED: {implementation_result.get('error')}[/red]")
                self.failed_solutions.append({
                    "problem": problem,
                    "error": implementation_result.get("error"),
                    "timestamp": datetime.now().isoformat()
                })

        except Exception as e:
            logger.error(f"Problem solving failed for {problem['type']}: {e}")
            log_error_with_context(e, f"autonomous_problem_solving_failed for {problem['type']}", {"problem": str(problem)})

    async def _research_solutions(self, problem: Dict[str, Any], analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Research solutions for the problem using web search"""
        try:
            if not self.web_search:
                logger.warning("Web search tool not available for solution research")
                return []

            problem_type = problem["type"]
            solutions = []

            # Get search configuration
            web_config = self.autonomous_config.get_web_search_config()
            max_queries = web_config.get("max_queries_per_problem", 3)
            max_results = web_config.get("max_results_per_query", 5)
            max_content_length = web_config.get("max_content_length", 2000)
            search_delay = web_config.get("search_delay_seconds", 1)

            # Get search queries for this problem type
            if problem_type in self.problem_patterns:
                search_queries = self.problem_patterns[problem_type].get("search_queries", [])
            else:
                # Generate generic search queries
                search_queries = [
                    f"{problem_type} fix solution",
                    f"mobile automation {problem_type} troubleshooting",
                    f"android testing {problem_type} resolution"
                ]

            # Search for solutions using each query
            for query in search_queries[:max_queries]:  # Use configured limit
                try:
                    console.print(f"[cyan]🔍 Searching: {query}[/cyan]")

                    # Use web search tool (similar to self-learning)
                    search_results = await self.web_search(query=query, num_results=max_results)

                    if search_results and hasattr(search_results, 'results'):
                        for result in search_results.results[:max_results]:  # Use configured limit
                            # Fetch content from the result
                            if self.web_fetch and hasattr(result, 'url'):
                                try:
                                    content = await self.web_fetch(url=result.url)
                                    if content:
                                        solution = {
                                            "query": query,
                                            "url": result.url,
                                            "title": getattr(result, 'title', 'Unknown'),
                                            "content": content[:max_content_length],  # Use configured limit
                                            "relevance_score": self._calculate_relevance_score(content, problem),
                                            "source": "web_search"
                                        }
                                        solutions.append(solution)
                                except Exception as fetch_e:
                                    logger.debug(f"Failed to fetch content from {result.url}: {fetch_e}")

                    # Configurable delay between searches
                    await asyncio.sleep(search_delay)

                except Exception as search_e:
                    logger.warning(f"Search failed for query '{query}': {search_e}")
                    continue

            # Sort solutions by relevance score
            solutions.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            console.print(f"[green]📚 Found {len(solutions)} potential solutions[/green]")
            return solutions[:5]  # Return top 5 solutions

        except Exception as e:
            logger.error(f"Solution research failed: {e}")
            return []

    def _calculate_relevance_score(self, content: str, problem: Dict[str, Any]) -> float:
        """Calculate relevance score for a solution based on content and problem"""
        try:
            score = 0.0
            content_lower = content.lower()
            problem_type = problem["type"].lower()

            # Check for problem type keywords
            if problem_type in self.problem_patterns:
                keywords = self.problem_patterns[problem_type]["keywords"]
                for keyword in keywords:
                    if keyword.lower() in content_lower:
                        score += 1.0

            # Check for solution indicators
            solution_indicators = [
                "fix", "solution", "resolve", "troubleshoot", "workaround",
                "configure", "setting", "parameter", "optimization", "improve"
            ]

            for indicator in solution_indicators:
                if indicator in content_lower:
                    score += 0.5

            # Check for technical terms related to mobile automation
            tech_terms = [
                "uiautomator2", "appium", "android", "mobile", "automation",
                "element", "xpath", "locator", "timeout", "performance"
            ]

            for term in tech_terms:
                if term in content_lower:
                    score += 0.3

            return min(score, 10.0)  # Cap at 10.0

        except Exception as e:
            logger.warning(f"Failed to calculate relevance score: {e}")
            return 0.0

    async def _implement_best_solution(self, problem: Dict[str, Any], solutions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Implement the best solution for the problem"""
        try:
            if not solutions:
                return {"success": False, "error": "No solutions available"}

            # Get the highest scoring solution
            best_solution = solutions[0]

            console.print(f"[cyan]🔧 Implementing solution from: {best_solution.get('title', 'Unknown source')}[/cyan]")

            # Analyze the solution content to determine implementation strategy
            implementation_strategy = self._determine_implementation_strategy(best_solution, problem)

            if implementation_strategy["type"] in self.solution_strategies:
                strategy_func = self.solution_strategies[implementation_strategy["type"]]
                result = await strategy_func(implementation_strategy, problem, best_solution)

                if result["success"]:
                    return {
                        "success": True,
                        "solution": {
                            "strategy": implementation_strategy,
                            "source": best_solution,
                            "implementation_result": result
                        }
                    }
                else:
                    return {"success": False, "error": result.get("error", "Implementation failed")}
            else:
                return {"success": False, "error": f"Unknown implementation strategy: {implementation_strategy['type']}"}

        except Exception as e:
            logger.error(f"Solution implementation failed: {e}")
            return {"success": False, "error": str(e)}

    def _determine_implementation_strategy(self, solution, problem):
        # Preserve sMTm's AI classifier
        nn_prediction = self.ai_classifier.predict(solution)
        
        # Add BACKUP's fallback rules
        if nn_prediction['confidence'] < 0.7:
            if 'timeout' in solution.content.lower():
                return {'type': 'timeout_adjustment'}
        content = solution.get("content", "").lower()
        problem_type = problem["type"]

        # Analyze content to determine strategy type
        if any(keyword in content for keyword in ["config", "configuration", "setting", "parameter"]):
            return {
                "type": "configuration_change",
                "details": self._extract_config_changes(content),
                "confidence": 0.8
            }
        elif any(keyword in content for keyword in ["timeout", "wait", "delay", "duration"]):
            return {
                "type": "timeout_adjustment",
                "details": self._extract_timeout_values(content),
                "confidence": 0.7
            }
        elif any(keyword in content for keyword in ["retry", "attempt", "repeat"]):
            return {
                "type": "retry_mechanism",
                "details": self._extract_retry_logic(content),
                "confidence": 0.6
            }
        elif any(keyword in content for keyword in ["detection", "strategy", "method", "approach"]):
            return {
                "type": "detection_strategy",
                "details": self._extract_detection_improvements(content),
                "confidence": 0.7
            }
        else:
            return {
                "type": "code_modification",
                "details": {"content": content[:500]},
                "confidence": 0.5
            }

        try:
            pass
        except Exception as e:
            logger.warning(f"Failed to determine implementation strategy: {e}")
            return {
                "type": "code_modification",
                "details": {"error": str(e)},
                "confidence": 0.1
            }

    def _extract_config_changes(self, content: str) -> Dict[str, Any]:
        """Extract configuration changes from solution content"""
        config_changes = {}

        # Look for common configuration patterns
        import re

        # Find timeout values
        timeout_matches = re.findall(r'timeout[:\s=]+(\d+)', content, re.IGNORECASE)
        if timeout_matches:
            config_changes["timeout"] = int(timeout_matches[0])

        # Find element count targets
        element_matches = re.findall(r'element[s]?[:\s=]+(\d+)', content, re.IGNORECASE)
        if element_matches:
            config_changes["target_elements"] = int(element_matches[0])

        # Find retry counts
        retry_matches = re.findall(r'retry[:\s=]+(\d+)', content, re.IGNORECASE)
        if retry_matches:
            config_changes["max_retries"] = int(retry_matches[0])

        return config_changes

    def _extract_timeout_values(self, content: str) -> Dict[str, Any]:
        """Extract timeout values from solution content"""
        import re

        timeouts = {}

        # Find various timeout patterns
        patterns = [
            (r'timeout[:\s=]+(\d+)', "general_timeout"),
            (r'wait[:\s=]+(\d+)', "wait_timeout"),
            (r'delay[:\s=]+(\d+)', "delay_timeout"),
            (r'(\d+)\s*second', "seconds"),
            (r'(\d+)\s*minute', "minutes")
        ]

        for pattern, key in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                timeouts[key] = int(matches[0])

        return timeouts

    def _extract_retry_logic(self, content: str) -> Dict[str, Any]:
        """Extract retry logic from solution content"""
        import re

        retry_config = {}

        # Find retry patterns
        retry_matches = re.findall(r'retry[:\s=]+(\d+)', content, re.IGNORECASE)
        if retry_matches:
            retry_config["max_retries"] = int(retry_matches[0])

        # Find backoff patterns
        backoff_matches = re.findall(r'backoff[:\s=]+(\d+)', content, re.IGNORECASE)
        if backoff_matches:
            retry_config["backoff_seconds"] = int(backoff_matches[0])

        return retry_config

    def _extract_detection_improvements(self, content: str) -> Dict[str, Any]:
        """Extract detection improvement strategies from solution content"""
        improvements = {}

        content_lower = content.lower()

        # Check for specific detection strategies
        if "xpath" in content_lower:
            improvements["use_xpath"] = True
        if "resource-id" in content_lower or "resource_id" in content_lower:
            improvements["prefer_resource_id"] = True
        if "accessibility" in content_lower:
            improvements["use_accessibility_id"] = True
        if "parallel" in content_lower:
            improvements["enable_parallel_processing"] = True
        if "cache" in content_lower:
            improvements["enable_caching"] = True

        return improvements

    # Implementation strategy methods
    async def _implement_config_change(self, strategy: Dict[str, Any], problem: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        """Implement configuration changes"""
        try:
            config_changes = strategy.get("details", {})

            console.print(f"[cyan]⚙️ Applying configuration changes: {config_changes}[/cyan]")

            # Apply configuration changes
            changes_applied = []
            for key, value in config_changes.items():
                try:
                    # Update configuration
                    old_value = self.config.get(key.upper())
                    self.config.set(key.upper(), value)
                    changes_applied.append(f"{key}: {old_value} -> {value}")

                except Exception as e:
                    logger.warning(f"Failed to apply config change {key}={value}: {e}")

            if changes_applied:
                console.print(f"[green]✅ Applied {len(changes_applied)} configuration changes[/green]")
                return {
                    "success": True,
                    "changes_applied": changes_applied,
                    "strategy": "configuration_change"
                }
            else:
                return {"success": False, "error": "No configuration changes could be applied"}

        except Exception as e:
            logger.error(f"Configuration change implementation failed: {e}")
            return {"success": False, "error": str(e)}

    async def _implement_timeout_adjustment(self, strategy: Dict[str, Any], problem: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        """Implement timeout adjustments"""
        try:
            timeout_config = strategy.get("details", {})

            console.print(f"[cyan]⏱️ Adjusting timeouts: {timeout_config}[/cyan]")

            # Apply timeout adjustments
            adjustments = []

            if "general_timeout" in timeout_config:
                self.config.set("ELEMENT_WAIT_TIMEOUT", timeout_config["general_timeout"])
                adjustments.append(f"Element wait timeout: {timeout_config['general_timeout']}s")

            if "wait_timeout" in timeout_config:
                self.config.set("NAVIGATION_TIMEOUT", timeout_config["wait_timeout"])
                adjustments.append(f"Navigation timeout: {timeout_config['wait_timeout']}s")

            if adjustments:
                console.print(f"[green]✅ Applied {len(adjustments)} timeout adjustments[/green]")
                return {
                    "success": True,
                    "adjustments": adjustments,
                    "strategy": "timeout_adjustment"
                }
            else:
                return {"success": False, "error": "No timeout adjustments could be applied"}

        except Exception as e:
            logger.error(f"Timeout adjustment implementation failed: {e}")
            return {"success": False, "error": str(e)}

    async def _implement_retry_mechanism(self, strategy: Dict[str, Any], problem: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        # Merge BACKUP's system limit validation with sMTm's AI tracking
        system_wide_limit = self.config.get('SYSTEM_RETRY_LIMIT', 15)
        historical_success_rate = self._get_historical_success('retry')
        
        # Combined validation logic
        if problem.get('retry_count', 0) >= system_wide_limit:
            logger.warning(f"System-wide retry limit reached: {system_wide_limit}")
            return {"success": False, "error": "Global retry limit exceeded"}
            
        if historical_success_rate < self.config.get('MIN_SUCCESS_RATE', 0.2):
            logger.warning(f"Historical success rate too low: {historical_success_rate:.2f}")
            return {"success": False, "error": "Insufficient historical success for retry"}

        # Preserve sMTm's AI-driven improvements
        current_load = self.monitor.get_system_load()
        if current_load > self.config.get('MAX_LOAD_THRESHOLD', 0.8):
            logger.warning(f"System load too high for retry: {current_load:.2f}")
            return {"success": False, "error": "Resource constraints prevent retry"}

        # Apply sMTm's existing retry logic with enhanced validation
        try:
            # Preserve original AI-powered retry implementation
            result = await self._execute_retry_with_backoff(strategy, problem)
            
            # Enhanced success tracking from sMTm
            self.success_tracker.log_retry_attempt(
                problem_id=problem['id'],
                success=result['success'],
                strategy_hash=hash(str(strategy))
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Retry mechanism failed: {str(e)}")
            return {"success": False, "error": str(e)}
    
        except Exception as e:
            logger.error(f"Retry mechanism implementation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _implement_detection_strategy(self, strategy: Dict[str, Any], problem: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        """Implement detection strategy improvements"""
        try:
            improvements = strategy.get("details", {})
    
            console.print(f"[cyan]🔍 Implementing detection improvements: {improvements}[/cyan]")
    
            # Apply detection improvements
            detection_changes = []
    
            if improvements.get("prefer_resource_id"):
                self.config.set("PREFER_RESOURCE_ID_LOCATORS", True)
                detection_changes.append("Enabled resource-id preference")
    
            if improvements.get("use_accessibility_id"):
                self.config.set("ENABLE_ACCESSIBILITY_ID_DETECTION", True)
                detection_changes.append("Enabled accessibility-id detection")
    
            if improvements.get("enable_parallel_processing"):
                self.config.set("ENABLE_PARALLEL_ELEMENT_PROCESSING", True)
                detection_changes.append("Enabled parallel processing")
    
            if improvements.get("enable_caching"):
                self.config.set("ENABLE_ELEMENT_CACHING", True)
                detection_changes.append("Enabled element caching")
    
            if detection_changes:
                console.print(f"[green]✅ Applied {len(detection_changes)} detection improvements[/green]")
                return {
                    "success": True,
                    "detection_changes": detection_changes,
                    "strategy": "detection_strategy"
                }
            else:
                return {"success": False, "error": "No detection improvements could be applied"}
    
        except Exception as e:
            logger.error(f"Detection strategy implementation failed: {e}")
            return {"success": False, "error": str(e)}
    
    async def _implement_code_modification(self, strategy: Dict[str, Any], problem: Dict[str, Any], solution: Dict[str, Any]) -> Dict[str, Any]:
        """Implement code modifications (placeholder for future enhancement)"""
        try:
            console.print("[yellow]⚠️ Code modification strategy not yet implemented[/yellow]")
            console.print("[cyan]💡 Suggestion: Manual review of solution content recommended[/cyan]")
    
            # For now, just log the solution for manual review
            solution_content = strategy.get("details", {}).get("content", "")
    
            return {
                "success": False,
                "error": "Code modification strategy requires manual implementation",
                "suggestion": solution_content[:200] + "..." if len(solution_content) > 200 else solution_content
            }
    
        except Exception as e:
            logger.error(f"Code modification implementation failed: {e}")
            return {"success": False, "error": str(e)}
