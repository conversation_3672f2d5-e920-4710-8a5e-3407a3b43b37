"""
Configuration Manager for Autonomous Problem Solver
Loads and manages configuration from YAML files with fallback to defaults
"""

import yaml
import os
from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger

from utils.config import Config
from utils.console import get_centralized_console

console = get_centralized_console()

class AutonomousConfig:
    """
    Configuration manager for autonomous problem solver
    Loads configuration from YAML files with intelligent defaults
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.base_config = Config()
        self.config_file = config_file or "config/autonomous_problem_solver.yaml"
        self.config_data = {}
        self.defaults = self._get_default_config()
        
        # Load configuration
        self._load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values"""
        return {
            "problem_solver": {
                "check_interval_seconds": 15,
                "update_interval_seconds": 5,
                "max_concurrent_solutions": 3,
                "solution_timeout_minutes": 3,
                "auto_implement_solutions": True,
                "background_mode": True,
                "aggressive_mode": False,
                "safety_checks_enabled": True,
                "require_confirmation_for_risky_changes": True,
                "max_rollback_stack_size": 10
            },
            "monitoring": {
                "performance_window_minutes": 10,
                "trend_analysis_enabled": True,
                "alert_thresholds": {
                    "low_elements_threshold": 1500,
                    "critical_low_elements": 900,
                    "high_unknown_percent": 3.0,
                    "critical_unknown_percent": 10.0,
                    "max_duration_minutes": 60,
                    "critical_duration_minutes": 90,
                    "max_navigation_failures": 5,
                    "critical_navigation_failures": 15,
                    "min_performance_score": 60.0,
                    "critical_performance_score": 30.0
                }
            },
            "targets": {
                "target_elements_count": 3000,
                "min_acceptable_elements": 2000,
                "target_analysis_duration": 30,
                "target_performance_score": 85.0,
                "max_unknown_elements_percent": 3.0,
                "target_navigation_success_rate": 95.0
            },
            "web_search": {
                "max_queries_per_problem": 3,
                "max_results_per_query": 5,
                "max_content_length": 2000,
                "search_delay_seconds": 1,
                "fetch_timeout_seconds": 10,
                "relevance_threshold": 2.0,
                "quality_threshold": 3.0
            },
            "logging": {
                "console_log_level": "INFO",
                "file_log_level": "DEBUG",
                "log_directory": "logs/ai_analysis",
                "problem_solver_log": "autonomous_problem_solver.log",
                "solutions_log": "implemented_solutions.log",
                "performance_log": "analysis_performance.log",
                "max_log_files": 10,
                "log_rotation_size_mb": 50,
                "log_all_problems": True,
                "log_solution_research": True,
                "log_implementation_steps": True,
                "log_performance_metrics": True
            }
        }
    
    def _load_config(self):
        """Load configuration from YAML file with fallback to defaults"""
        try:
            config_path = Path(self.config_file)
            
            if config_path.exists():
                console.print(f"[cyan]📄 Loading autonomous problem solver config from: {config_path}[/cyan]")
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                
                # Merge with defaults
                self.config_data = self._deep_merge(self.defaults, file_config or {})
                console.print("[green]✅ Configuration loaded successfully[/green]")
                
            else:
                console.print(f"[yellow]⚠️ Config file not found: {config_path}, using defaults[/yellow]")
                self.config_data = self.defaults.copy()
                
                # Create default config file
                self._create_default_config_file(config_path)
                
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            console.print(f"[red]❌ Failed to load config: {e}, using defaults[/red]")
            self.config_data = self.defaults.copy()
    
    def _deep_merge(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _create_default_config_file(self, config_path: Path):
        """Create a default configuration file"""
        try:
            # Create directory if it doesn't exist
            config_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write default configuration
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.defaults, f, default_flow_style=False, indent=2)
            
            console.print(f"[green]✅ Created default config file: {config_path}[/green]")
            
        except Exception as e:
            logger.warning(f"Failed to create default config file: {e}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        Example: get("problem_solver.check_interval_seconds")
        """
        try:
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
            
        except Exception as e:
            logger.debug(f"Failed to get config value for {key_path}: {e}")
            return default
    
    def set(self, key_path: str, value: Any):
        """
        Set configuration value using dot notation
        Example: set("problem_solver.check_interval_seconds", 30)
        """
        try:
            keys = key_path.split('.')
            config = self.config_data
            
            # Navigate to the parent dictionary
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # Set the value
            config[keys[-1]] = value
            
        except Exception as e:
            logger.error(f"Failed to set config value for {key_path}: {e}")
    
    def get_problem_patterns(self) -> Dict[str, Any]:
        """Get problem detection patterns"""
        return self.get("problem_patterns", {})
    
    def get_solution_strategies(self) -> Dict[str, Any]:
        """Get solution implementation strategies"""
        return self.get("solution_strategies", {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """Get monitoring configuration"""
        return {
            "check_interval_seconds": self.get("problem_solver.check_interval_seconds", 15),
            "update_interval_seconds": self.get("monitoring.update_interval_seconds", 5),
            "max_concurrent_solutions": self.get("problem_solver.max_concurrent_solutions", 3),
            "solution_timeout_minutes": self.get("problem_solver.solution_timeout_minutes", 3),
            "auto_implement_enabled": self.get("problem_solver.auto_implement_solutions", True),
            "background_mode": self.get("problem_solver.background_mode", True),
            "performance_window_minutes": self.get("monitoring.performance_window_minutes", 10),
            "alert_thresholds": self.get("monitoring.alert_thresholds", {})
        }
    
    def get_web_search_config(self) -> Dict[str, Any]:
        """Get web search configuration"""
        return self.get("web_search", {})
    
    def get_targets(self) -> Dict[str, Any]:
        """Get target configuration"""
        return self.get("targets", {})
    
    def is_enabled(self, feature_path: str) -> bool:
        """Check if a feature is enabled"""
        return self.get(f"{feature_path}.enabled", False)
    
    def get_safety_level(self, strategy: str) -> str:
        """Get safety level for a solution strategy"""
        return self.get(f"solution_strategies.{strategy}.safety_level", "medium")
    
    def is_aggressive_mode(self) -> bool:
        """Check if aggressive mode is enabled"""
        return self.get("problem_solver.aggressive_mode", False)
    
    def get_log_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return self.get("logging", {})
    
    def save_config(self, config_path: Optional[str] = None):
        """Save current configuration to file"""
        try:
            save_path = Path(config_path or self.config_file)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
            
            console.print(f"[green]✅ Configuration saved to: {save_path}[/green]")
            
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            console.print(f"[red]❌ Failed to save config: {e}[/red]")
    
    def reload_config(self):
        """Reload configuration from file"""
        self._load_config()
        console.print("[green]🔄 Configuration reloaded[/green]")
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration data"""
        return self.config_data.copy()
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return validation results"""
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": []
        }
        
        try:
            # Validate check intervals
            check_interval = self.get("problem_solver.check_interval_seconds", 15)
            if check_interval < 5:
                validation_results["warnings"].append("Check interval < 5 seconds may impact performance")
            elif check_interval > 60:
                validation_results["warnings"].append("Check interval > 60 seconds may delay problem detection")
            
            # Validate timeout values
            solution_timeout = self.get("problem_solver.solution_timeout_minutes", 3)
            if solution_timeout > 10:
                validation_results["warnings"].append("Solution timeout > 10 minutes may delay analysis")
            
            # Validate target elements
            target_elements = self.get("targets.target_elements_count", 3000)
            if target_elements < 1000:
                validation_results["warnings"].append("Target elements < 1000 may indicate low analysis quality")
            
            # Validate thresholds
            low_threshold = self.get("monitoring.alert_thresholds.low_elements_threshold", 1500)
            if low_threshold > target_elements * 0.8:
                validation_results["warnings"].append("Low elements threshold too high relative to target")
            
        except Exception as e:
            validation_results["valid"] = False
            validation_results["errors"].append(f"Validation error: {e}")
        
        return validation_results
